<?php

namespace App;

use Symfony\Bundle\FrameworkBundle\Kernel\MicroKernelTrait;
use Symfony\Component\DependencyInjection\Compiler\CompilerPassInterface;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\HttpKernel\Kernel as BaseKernel;
use Symfony\Component\Yaml\Yaml;

class Kernel extends BaseKernel implements CompilerPassInterface
{
    use MicroKernelTrait;

    public function process(ContainerBuilder $container): void
    {
        // in this method you can manipulate the service container:
        // for example, changing some container service:
        $projectDir = $container->getParameter('kernel.project_dir');
        $swarmpitConfigFilePath = sprintf('%s/config/swarmpit.yaml', $projectDir);

        if (file_exists($swarmpitConfigFilePath)) {
            $config = Yaml::parseFile($swarmpitConfigFilePath);
            $container->setParameter('swarmpit.config', $config);
        }
    }
}
