<?php

namespace App\MessageHandler;

use App\Entity\WebHost\WebHost;
use App\Entity\WebHost\WebHostConfiguration\DockerSwarmConfiguration;
use App\Message\RefreshSwarmWebHostsMessage;
use App\Model\Server;
use App\Model\Swarmpit\SwarmpitService;
use App\Service\HealthCheck\HealthChecker;
use App\Service\MercureUpdater;
use App\Service\SwarmpitAPI;
use Doctrine\Persistence\ManagerRegistry;
use Doctrine\Persistence\ObjectManager;
use Doctrine\Persistence\ObjectRepository;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
final class RefreshSwarmWebHostsMessageHandler
{
    private readonly ObjectRepository $repository;
    private readonly ObjectManager $entityManager;

    public function __construct(
        protected MercureUpdater $updater,
        protected HealthChecker $checker,
        protected SwarmpitAPI $api,
        protected ManagerRegistry $registry,
        #[Autowire(env: 'FETCH_CONCURRENCY')] protected int $concurrency,
    ) {
        $this->repository = $this->registry->getRepository(WebHost::class);
        $this->entityManager = $this->registry->getManager();
    }

    public function __invoke(RefreshSwarmWebHostsMessage $message): void
    {
        foreach ($message->services as $service) {
            /** @var WebHost $webHost */
            $webHost = $this->registry->getManagerForClass(WebHost::class)
                ->createQueryBuilder()
                ->select('w')
                ->from(WebHost::class, 'w')
                ->join(DockerSwarmConfiguration::class, 'c', 'WITH', 'c = w.configuration')
                ->andWhere('c.serviceName = :serviceName')
                ->setParameter('serviceName', $service['service']->serviceName)
                ->getQuery()
                ->getOneOrNullResult()
            ;
            $formattedService = $this->api->formatService($service['server'], $service['service']);
            if ($webHost instanceof WebHost) {
                $this->refreshWebHost($webHost, $service['server'], $service['service'], $formattedService);
            } else {
                $this->createWebHost($service['server'], $service['service'], $formattedService);
            }
        }
    }

    private function createWebHost(Server $server, SwarmpitService $service, array $formattedService): WebHost
    {
        $webHost = WebHost::fromSwarmpitService($service, $server, $formattedService);
        $this->entityManager->persist($webHost);
        $this->entityManager->flush();

        return $webHost;
    }

    private function refreshWebHost(WebHost $webHost, Server $server, SwarmpitService $service, array $formattedService): void
    {
        $webHost = $webHost->updateFromSwarmpitService($service, $server, $formattedService);
        $this->entityManager->persist($webHost);
        $this->entityManager->flush();
    }
}
