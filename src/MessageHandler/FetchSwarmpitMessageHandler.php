<?php

namespace App\MessageHandler;

use App\Message\FetchSwarmpitMessage;
use App\Message\RefreshSwarmWebHostsMessage;
use App\Model\Server;
use App\Model\Swarmpit\SwarmpitService;
use App\Service\MercureUpdater;
use App\Service\SwarmpitAPI;
use Spatie\Fork\Fork;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Symfony\Component\Messenger\MessageBusInterface;

#[AsMessageHandler]
final class FetchSwarmpitMessageHandler
{
    public function __construct(
        protected MercureUpdater $updater,
        protected SwarmpitAPI $api,
        #[Autowire(env: 'FETCH_CONCURRENCY')] protected int $concurrency,
        private readonly MessageBusInterface $bus
    ) {
    }

    public function __invoke(FetchSwarmpitMessage $message): void
    {
        dump($message);

        $this->api->setNoCache(true);

        $promises = [];

        foreach ($message->services as $service) {
            $promises[] = function () use ($service, $message) {
                echo 'fetch ' . $service['serverName'] . ' : ' . $service['serviceId'] . "\n";
                $response = $this->fetchService($service, $message);
                echo 'fetch ' . $service['serverName'] . ' : ' . $service['serviceId'] . ' terminé' . "\n";

                return $response;
            };
        }

        $responses = Fork::new()
            ->concurrent($this->concurrency)
            ->run(...$promises)
        ;

        // filtrage des services dont la récupération a échouée
        $validResponses = array_filter($responses, fn ($response) => $response['service'] instanceof SwarmpitService && $response['server'] instanceof Server);
        // récupération des services frontaux uniquement
        $frontWebServiceReponses = array_filter($validResponses, fn ($response) => $response['service']->isFrontWeb());
        // création ou mise à jour de l'hébergement lié au service swarm
        $this->bus->dispatch(new RefreshSwarmWebHostsMessage($frontWebServiceReponses));
    }

    public function fetchService(array $service, FetchSwarmpitMessage $message): array
    {
        $server = null;
        $swarmpitService = null;

        try {
            $serverName = $service['serverName'];
            $serviceId = $service['serviceId'];

            $server = $this->api->getServer($serverName);
            // récupération des informations du service
            $swarmpitService = $this->api->getServiceAndTasksInfos($server, $serviceId, false);
            echo 'fetch ' . $swarmpitService->serviceName . "\n";

            // activation du cache pour ne pas refaire les appels précédents mais récupérer les détails en plus
            $this->api->setNoCache(false);
            $swarmpitService = $this->api->getServiceAndTasksInfos($server, $serviceId, true);

            // désactivation du cache pour les appels suivants
            $this->api->setNoCache(true);

            $formattedService = $this->api->formatService($server, $swarmpitService);
            $this->updater->updateService($formattedService);
        } catch (\Exception $exception) {
            echo 'error ' . $exception->getMessage() . "\n";
            $this->updater->sendError($serviceId, $exception->getMessage());
        }

        return [
            'server' => $server,
            'service' => $swarmpitService,
        ];
    }
}
