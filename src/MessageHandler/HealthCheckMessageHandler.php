<?php

namespace App\MessageHandler;

use App\Entity\WebHost\WebHostUrl;
use App\Message\HealthCheckMessage;
use App\Service\HealthCheck\HealthChecker;
use App\Service\MercureUpdater;
use Doctrine\Persistence\ManagerRegistry;
use Doctrine\Persistence\ObjectManager;
use Doctrine\Persistence\ObjectRepository;
use Spatie\Fork\Fork;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
final class HealthCheckMessageHandler
{
    private readonly ObjectRepository $repository;
    private readonly ObjectManager $entityManager;

    public function __construct(
        protected MercureUpdater $updater,
        protected HealthChecker $checker,
        #[Autowire(env: 'FETCH_CONCURRENCY')] protected int $concurrency,
        ManagerRegistry $registry,
    ) {
        $this->repository = $registry->getRepository(WebHostUrl::class);
        $this->entityManager = $registry->getManager();
    }

    public function __invoke(HealthCheckMessage $message): void
    {
        $promises = [];

        foreach ($message->webHostUrlIds as $webHostUrlId) {
            $promises[] = function () use ($webHostUrlId, $message) {
                $this->updateWebHostUrlReport($webHostUrlId, $message);

                return $webHostUrlId;
            };
        }

        foreach ($promises as $promise) {
            $promise();
        }

        //        Fork::new()
        //            ->concurrent($this->concurrency)
        //            ->run(...$promises)
        //        ;
    }

    public function updateWebHostUrlReport(array $webHostUrlDatas, HealthCheckMessage $message): void
    {
        try {
            $webHostUrlId = $webHostUrlDatas['webHostUrlId'];
            $webHostUrl = $this->repository->findOneBy(['id' => $webHostUrlId]);

            if (!$webHostUrl instanceof WebHostUrl) {
                throw new \Exception('WebHostUrl not found');
            }

            $report = $this->checker->__invoke($webHostUrl->getUrl());
            $webHostUrl->setHealthCheckReport($report);
            $this->entityManager->persist($webHostUrl);
            $this->entityManager->flush();

            $this->updater->updateWebHost($webHostUrl->getWebHost());
        } catch (\Exception) {
            //            $this->updater->sendError($serviceId, $exception->getMessage());
        }
    }
}
