<?php

namespace App\MessageHandler;

use App\Message\RedeployServicesMessage;
use App\Service\MercureUpdater;
use App\Service\SwarmpitAPI;
use Spatie\Fork\Fork;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
final class RedeployServicesMessageHandler
{
    public function __construct(
        protected MercureUpdater $updater,
        protected SwarmpitAPI $api,
        #[Autowire(env: 'FETCH_CONCURRENCY')] protected int $concurrency
    ) {
    }

    public function __invoke(RedeployServicesMessage $message): void
    {
        dump($message);

        $this->api->setNoCache(true);

        $promises = [];

        foreach ($message->services as $service) {
            $promises[] = function () use ($service, $message) {
                echo 'redeploie ' . $service['serverName'] . ' : ' . $service['serviceId'] . "\n";
                $this->redeployService($service, $message);
                echo 'redeploie ' . $service['serverName'] . ' : ' . $service['serviceId'] . ' terminé' . "\n";

                return $service;
            };
        }

        Fork::new()
            ->concurrent($this->concurrency)
            ->run(...$promises)
        ;
    }

    public function redeployService(array $service, RedeployServicesMessage $message): array
    {
        $serverName = $service['serverName'];
        $serviceId = $service['serviceId'];

        try {
            $this->api->redeploy($serverName, $serviceId, $message->tag);
        } catch (\Exception $e) {
            $this->updater->sendError($serviceId, $e->getMessage());

            return $service;
        }

        $server = $this->api->getServer($serverName);
        $ready = false;
        $maxRetry = 30;
        $retries = 0;
        $hasError = false;

        while (!$ready && !$hasError && $retries <= $maxRetry) {
            ++$retries;
            try {
                $service = $this->api->getServiceAndTasksInfos($server, $serviceId, false);
                $formattedService = $this->api->formatService($server, $service);

                $this->updater->updateService($formattedService);
                dump($serviceId . ' retry :' . $service->status->update);
                if ('updating' !== $service->status->update) {
                    $ready = true;
                }
                sleep(5);
            } catch (\Exception $e) {
                $this->updater->sendError($serviceId, $e->getMessage());
                $hasError = true;
            }
        }

        if ($ready) {
            try {
                $this->updater->sendReloadingEnd($serviceId);
            } catch (\Exception $e) {
                dump($e);
            }
            dump($serviceId . ' updated');
        } else {
            dump($serviceId . ' stopped retry');
        }

        return $service;
    }
}
