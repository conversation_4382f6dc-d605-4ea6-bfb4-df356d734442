<?php

namespace App\Scheduler;

use Symfony\Component\Console\Messenger\RunCommandMessage;
use Symfony\Component\Console\Messenger\RunCommandMessageHandler as OriginalHandler;
use Symfony\Component\Console\Output\ConsoleOutput;
use Symfony\Component\DependencyInjection\Attribute\AsDecorator;
use Symfony\Component\DependencyInjection\Attribute\AutowireDecorated;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
#[AsDecorator(decorates: 'console.messenger.execute_command_handler')]
final readonly class RunCommandMessageHandler
{
    public function __construct(
        #[AutowireDecorated] private OriginalHandler $commandMessageHandler,
    ) {
    }

    public function __invoke(RunCommandMessage $message): void
    {
        $output = new ConsoleOutput();
        $output->writeln($message->input);
        $output->writeln('----------');

        $result = $this->commandMessageHandler->__invoke($message);

        $commandOutput = trim($result->output);

        $output->writeln($commandOutput);
        $output->writeln('----------');
    }
}
