<?php

namespace App\Form;

use App\Entity\WebHost\WebHostConfiguration\DockerSwarmConfiguration;
use App\Entity\WebHost\WebHostConfiguration\VirtualMachineConfiguration;
use App\Entity\WebHost\WebHostEnvironnement;
use App\Form\DTO\EditWebHostDTO;
use App\Model\DockerSwarmCluster;
use App\Model\WebsiteVisibility;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\CallbackTransformer;
use Symfony\Component\Form\Extension\Core\Type\EnumType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\OptionsResolver\OptionsResolver;

class EditWebHostType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('name', TextType::class, [
                'label' => 'Nom',
                'required' => false,
            ])
            ->add('urls', TextareaType::class, [
                'label' => 'URLs',
                'attr' => [
                    'placeholder' => 'https://www.example.com; https://www.example.com/fr',
                ],
                'help' => 'Séparez les URLs par un point-virgule',
            ])
            ->add('confluenceUrl', TextType::class, [
                'label' => 'Confluence',
                'required' => false,
            ])
            ->add('databaseExplorerUrl', TextType::class, [
                'label' => 'PhpMyAdmin / Adminer',
                'required' => false,
            ])
            ->add('associatedUrls', TextareaType::class, [
                'label' => 'Autres URLs liées et liens de documentation',
                'attr' => [
                    'placeholder' => 'http://documentation.example.com; https://app.clickup.com/1374946/czez',
                ],
                'help' => 'Séparez les URLs par un point-virgule',
                'required' => false,
            ])
            ->add('expectedVisibility', EnumType::class, [
                'label' => 'Visibilité attendue',
                'class' => WebsiteVisibility::class,
                'placeholder' => '',
            ])
            ->add('environnement', EnumType::class, [
                'label' => 'Environnement',
                'class' => WebHostEnvironnement::class,
                'placeholder' => '',
            ])
            ->add('gitlabRemoteUrl', TextType::class, [
                'label' => 'URL du dépôt Gitlab',
                'required' => false,
            ])
            ->add('gitlabActiveBranch', TextType::class, [
                'label' => 'Branch active',
                'required' => false,
            ])
            ->add('webId', TextType::class, [
                'label' => 'Web ID',
                'required' => false,
            ])
        ;

        // Ajout dynamique des champs de configuration selon le type
        $builder->addEventListener(FormEvents::PRE_SET_DATA, function (FormEvent $event) {
            $data = $event->getData();
            $form = $event->getForm();

            if ($data && $data->configuration) {
                if ($data->configuration instanceof VirtualMachineConfiguration) {
                    $form->add('vmIp', TextType::class, [
                        'label' => 'Adresse IP',
                        'required' => false,
                        'help' => 'Adresse IP de la machine virtuelle',
                    ])
                    ->add('vmLocation', TextType::class, [
                        'label' => 'Emplacement',
                        'required' => false,
                        'help' => 'Chemin ou emplacement de la machine virtuelle',
                    ]);
                } elseif ($data->configuration instanceof DockerSwarmConfiguration) {
                    $form->add('swarmClusterName', EnumType::class, [
                        'label' => 'Nom du cluster',
                        'class' => DockerSwarmCluster::class,
                        'required' => false,
                        'help' => 'Nom du cluster Docker Swarm',
                    ])
                    ->add('swarmSwarmpitUrl', TextType::class, [
                        'label' => 'URL Swarmpit',
                        'required' => false,
                        'help' => 'URL d\'accès à l\'interface Swarmpit',
                    ])
                    ->add('swarmStack', TextType::class, [
                        'label' => 'Stack',
                        'required' => false,
                        'help' => 'Nom de la stack Docker',
                    ])
                    ->add('swarmServiceName', TextType::class, [
                        'label' => 'Nom du service',
                        'required' => false,
                        'help' => 'Nom du service dans la stack',
                    ]);
                }
            }
        });

        $stringToArrayTransformer = new CallbackTransformer(
            function ($tagsAsArray): ?string {
                // transform the array to a string
                return null !== $tagsAsArray ? implode('; ', $tagsAsArray) : null;
            },
            function ($tagsAsString): array {
                if (null === $tagsAsString) {
                    return [];
                }

                // transform the string back to an array
                return array_map('trim', explode(';', $tagsAsString));
            }
        );
        $builder->get('urls')->addModelTransformer($stringToArrayTransformer);
        $builder->get('associatedUrls')->addModelTransformer($stringToArrayTransformer);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => EditWebHostDTO::class,
        ]);
    }
}
