<?php

namespace App\Form\DTO;

use App\Entity\WebHost\WebHost;
use App\Entity\WebHost\WebHostConfiguration\DockerSwarmConfiguration;
use App\Entity\WebHost\WebHostConfiguration\VirtualMachineConfiguration;
use App\Entity\WebHost\WebHostConfiguration\WebHostConfiguration;
use App\Entity\WebHost\WebHostEnvironnement;
use App\Entity\WebHost\WebHostUrl;
use App\Model\DockerSwarmCluster;
use App\Model\WebsiteVisibility;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Context\ExecutionContextInterface;

class EditWebHostDTO
{
    public function __construct(
        public ?string $name,
        #[Assert\NotBlank]
        public array $urls,
        #[Assert\NotBlank]
        public ?WebsiteVisibility $expectedVisibility,
        #[Assert\NotBlank]
        public ?WebHostEnvironnement $environnement,
        #[Assert\Url]
        public ?string $confluenceUrl = null,
        #[Assert\Url]
        public ?string $databaseExplorerUrl = null,
        public array $associatedUrls = [],
        public ?string $gitlabRemoteUrl = null,
        public ?string $gitlabActiveBranch = null,
        public ?string $webId = null,
        // Champs pour VirtualMachineConfiguration
        public ?string $vmIp = null,
        public ?string $vmLocation = null,
        // Champs pour DockerSwarmConfiguration
        public ?DockerSwarmCluster $swarmClusterName = null,
        public ?string $swarmSwarmpitUrl = null,
        public ?string $swarmStack = null,
        public ?string $swarmServiceName = null,
        public ?WebHostConfiguration $configuration = null,
    ) {
    }

    #[Assert\Callback]
    public function validateUrls(ExecutionContextInterface $context, mixed $payload): void
    {
        if (count($this->urls) < 1 || empty($this->urls[0])) {
            $context->buildViolation('Vous devez spécifier au moins une URL')
                ->atPath('urls')
                ->addViolation();

            return;
        }

        foreach ($this->urls as $url) {
            if (!filter_var($url, FILTER_VALIDATE_URL)) {
                $context->buildViolation('Toutes les URL doivent être valides')
                    ->atPath('urls')
                    ->addViolation();
            }
        }
    }

    #[Assert\Callback]
    public function validateAssociatedUrls(ExecutionContextInterface $context, mixed $payload): void
    {
        foreach ($this->associatedUrls as $url) {
            if (!filter_var($url, FILTER_VALIDATE_URL)) {
                $context->buildViolation('Toutes les URL doivent être valides')
                    ->atPath('associatedUrls')
                    ->addViolation();
            }
        }
    }

    public static function fromEntity(WebHost $webHost): self
    {
        $configuration = $webHost->getConfiguration();

        // Initialisation des champs spécifiques selon le type de configuration
        $vmIp = null;
        $vmLocation = null;
        $swarmClusterName = null;
        $swarmSwarmpitUrl = null;
        $swarmStack = null;
        $swarmServiceName = null;

        if ($configuration instanceof VirtualMachineConfiguration) {
            $vmIp = $configuration->getIp();
            $vmLocation = $configuration->getLocation();
        } elseif ($configuration instanceof DockerSwarmConfiguration) {
            $swarmClusterName = $configuration->getClusterName();
            $swarmSwarmpitUrl = $configuration->getSwarmpitUrl();
            $swarmStack = $configuration->getStack();
            $swarmServiceName = $configuration->getServiceName();
        }

        return new self(
            name: $webHost->getName(),
            urls: $webHost->getUrls()->map(fn (WebHostUrl $webHostUrl) => $webHostUrl->getUrl())->toArray(),
            expectedVisibility: $webHost->getExpectedVisibility(),
            environnement: $webHost->getEnvironnement(),
            confluenceUrl: $webHost->getConfluenceUrl(),
            databaseExplorerUrl: $webHost->getDatabaseExplorerUrl(),
            associatedUrls: $webHost->getAssociatedUrls(),
            gitlabRemoteUrl: $webHost->getGitlabRemoteUrl(),
            gitlabActiveBranch: $webHost->getGitlabActiveBranch(),
            webId: $configuration?->getWebId(),
            vmIp: $vmIp,
            vmLocation: $vmLocation,
            swarmClusterName: $swarmClusterName,
            swarmSwarmpitUrl: $swarmSwarmpitUrl,
            swarmStack: $swarmStack,
            swarmServiceName: $swarmServiceName,
            configuration: $configuration,
        );
    }

    public function toEntity(WebHost $webHost): WebHost
    {
        $submittedDomains = array_map(fn (string $url) => parse_url($url, PHP_URL_HOST), $this->urls);
        $existingUrls = $webHost->getUrls();
        $existingDomains = $existingUrls->map(fn (WebHostUrl $webHostUrl) => $webHostUrl->getDomain())->toArray();

        // création des nouvelles URLs
        foreach ($this->urls as $url) {
            $domain = parse_url((string) $url, PHP_URL_HOST);
            if (!in_array($domain, $existingDomains, true)) {
                $webHostUrl = new WebHostUrl();
                $webHostUrl->setUrl($url);
                $webHost->addUrl($webHostUrl);
                $existingDomains[] = $domain;
            }
        }

        // nettoyage des URLs existantes qui ont été supprimées de la liste
        foreach ($existingUrls as $webHostUrl) {
            if (!in_array($webHostUrl->getDomain(), $submittedDomains, true)) {
                $webHost->removeUrl($webHostUrl);
            }
        }

        // mise à jour des autres champs
        $webHost->setName($this->name);
        $webHost->setExpectedVisibility($this->expectedVisibility);
        $webHost->setEnvironnement($this->environnement);
        $webHost->setConfluenceUrl($this->confluenceUrl);
        $webHost->setDatabaseExplorerUrl($this->databaseExplorerUrl);
        $webHost->setAssociatedUrls($this->associatedUrls);
        $webHost->setGitlabRemoteUrl($this->gitlabRemoteUrl);
        $webHost->setGitlabActiveBranch($this->gitlabActiveBranch);

        // Mise à jour de la configuration avec les champs spécifiques
        $configuration = $this->configuration;
        $configuration->setWebId($this->webId);
        if ($configuration instanceof VirtualMachineConfiguration) {
            $configuration->setIp($this->vmIp);
            $configuration->setLocation($this->vmLocation);
        } elseif ($configuration instanceof DockerSwarmConfiguration) {
            $configuration->setClusterName($this->swarmClusterName);
            $configuration->setSwarmpitUrl($this->swarmSwarmpitUrl);
            $configuration->setStack($this->swarmStack);
            $configuration->setServiceName($this->swarmServiceName);
        }

        $webHost->setConfiguration($configuration);

        return $webHost;
    }
}
