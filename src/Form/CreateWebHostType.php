<?php

namespace App\Form;

use App\Entity\WebHost\WebHostEnvironnement;
use App\Form\DTO\CreateWebHostDTO;
use App\Model\WebsiteVisibility;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\CallbackTransformer;
use Symfony\Component\Form\Extension\Core\Type\EnumType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class CreateWebHostType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('name', TextType::class, [
                'label' => 'Nom',
                'required' => false,
            ])
            ->add('urls', TextareaType::class, [
                'label' => 'URLs',
                'attr' => [
                    'placeholder' => 'https://www.example.com; https://www.example.com/fr',
                ],
                'help' => 'Séparez les URLs par un point-virgule',
            ])
            ->add('confluenceUrl', TextType::class, [
                'label' => 'Confluence',
                'required' => false,
            ])
            ->add('databaseExplorerUrl', TextType::class, [
                'label' => 'PhpMyAdmin / Adminer',
                'required' => false,
            ])
            ->add('associatedUrls', TextareaType::class, [
                'label' => 'Autres URLs liées et liens de documentation',
                'attr' => [
                    'placeholder' => 'http://documentation.example.com; https://app.clickup.com/1374946/czez',
                ],
                'help' => 'Séparez les URLs par un point-virgule',
                'required' => false,
            ])
            ->add('expectedVisibility', EnumType::class, [
                'label' => 'Visibilité attendue',
                'class' => WebsiteVisibility::class,
                'placeholder' => '',
            ])
            ->add('environnement', EnumType::class, [
                'label' => 'Environnement',
                'class' => WebHostEnvironnement::class,
                'placeholder' => '',
            ])
            ->add('gitlabRemoteUrl', TextType::class, [
                'label' => 'URL du dépôt Gitlab',
                'required' => false,
            ])
            ->add('gitlabActiveBranch', TextType::class, [
                'label' => 'Branch active',
                'required' => false,
            ])
        ;

        $stringToArrayTransformer = new CallbackTransformer(
            function ($tagsAsArray): ?string {
                // transform the array to a string
                return null !== $tagsAsArray ? implode('; ', $tagsAsArray) : null;
            },
            function ($tagsAsString): array {
                if (null === $tagsAsString) {
                    return [];
                }

                // transform the string back to an array
                return array_map('trim', explode(';', $tagsAsString));
            }
        );
        $builder->get('urls')->addModelTransformer($stringToArrayTransformer);
        $builder->get('associatedUrls')->addModelTransformer($stringToArrayTransformer);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => CreateWebHostDTO::class,
        ]);
    }
}
