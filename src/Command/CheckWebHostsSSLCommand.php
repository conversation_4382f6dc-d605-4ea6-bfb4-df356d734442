<?php

namespace App\Command;

use App\Entity\WebHost\WebHostUrl;
use App\Message\CheckSSLCertificatesMessage;
use Doctrine\Persistence\ManagerRegistry;
use Doctrine\Persistence\ObjectManager;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Messenger\MessageBusInterface;

#[AsCommand(
    name: self::COMMAND_NAME,
    description: 'Check de l\'état de santé des hébergements',
)]
class CheckWebHostsSSLCommand
{
    public const string COMMAND_NAME = 'app:webHosts:ssl-check';
    private readonly ObjectManager $entityManager;

    public function __construct(
        private readonly MessageBusInterface $bus,
        ManagerRegistry $doctrine,
    ) {
        $this->entityManager = $doctrine->getManager();
    }

    public function __invoke(OutputInterface $output): int
    {
        $output->writeln('Check de la validité des certificats SSL des hébergements ...');

        $webHostUrls = $this->entityManager->getRepository(WebHostUrl::class)->findAll();
        $messageArguments = array_map(fn ($webHostUrl) => ['webHostUrlId' => $webHostUrl->getId()], $webHostUrls);

        $this->bus->dispatch(new CheckSSLCertificatesMessage($messageArguments));

        return Command::SUCCESS;
    }
}
