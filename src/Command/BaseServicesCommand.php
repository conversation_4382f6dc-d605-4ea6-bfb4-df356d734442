<?php

namespace App\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Process\Process;

abstract class BaseServicesCommand extends Command
{
    /**
     * @param string[] $values
     */
    public function getServicesFromArguments(array $values): array
    {
        $services = [];
        foreach ($values as $item) {
            [$serverName, $serviceId] = explode(':', $item);
            $services[] = [
                'serverName' => $serverName,
                'serviceId' => $serviceId,
            ];
        }

        return $services;
    }

    /**
     * @param array{serverName: string, serviceId: string} $services
     */
    public static function getArgumentsFromServices(array $services)
    {
        $values = array_map(function (array $service) {
            return $service['serverName'] . ':' . $service['serviceId'];
        }, $services);

        return join(' ', $values);
    }

    /**
     * @param array{serverName: string, serviceId: string} $services
     */
    public static function getAsProcess(array $services, string $projectDir, array $options = [], $background = false)
    {
        $command = sprintf('%s %s', static::getDefaultName(), static::getArgumentsFromServices($services));
        foreach ($options as $option => $value) {
            $command = sprintf('%s --%s=%s', $command, $option, $value);
        }
        $cmd = sprintf('php %s/bin/console %s', $projectDir, $command);
        if ($background) {
            $cmd = sprintf('%s >/dev/null 2>&1 &', $cmd);
        }

        return Process::fromShellCommandline($cmd);
    }
}
