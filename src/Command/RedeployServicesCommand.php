<?php

namespace App\Command;

use App\Service\MercureUpdater;
use App\Service\SwarmpitAPI;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name: 'app:redeploy-services',
    description: 'Redeploy un ou plusieurs services et envoi les infos aux front en temps réel',
)]
class RedeployServicesCommand extends BaseServicesCommand
{
    public function __construct(
        private readonly MercureUpdater $updater,
        private readonly SwarmpitAPI $api
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addArgument('services', InputArgument::IS_ARRAY, 'Liste des services à redeployer, au format "{serverName}:{serviceId}, séparés par des espaces"')
            ->addOption('tag', 't', InputOption::VALUE_OPTIONAL, 'Tag de version à déployer (optionel)')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $services = $this->getServicesFromArguments($input->getArgument('services'));
        $tag = $input->getOption('tag');

        $this->api->setNoCache(true);

        foreach ($services as $service) {
            $serverName = $service['serverName'];
            $serviceId = $service['serviceId'];

            try {
                $this->api->redeploy($serverName, $serviceId, $tag);
            } catch (\Exception $e) {
                $this->updater->sendError($serviceId, $e->getMessage());
                $output->writeln($e->getMessage());
                continue;
            }

            sleep(1);

            $server = $this->api->getServer($serverName);
            $ready = false;
            $maxRetry = 30;
            $retries = 0;
            $hasError = false;

            while (!$ready && !$hasError && $retries <= $maxRetry) {
                ++$retries;
                try {
                    $service = $this->api->getServiceAndTasksInfos($server, $serviceId, false);
                    $formattedService = $this->api->formatService($server, $service);

                    $this->updater->updateService($formattedService);
                    $output->writeln($serviceId . ' retry :' . $service->status->update);
                    if ('updating' !== $service->status->update) {
                        $ready = true;
                    }
                    sleep(2);
                } catch (\Exception $e) {
                    $this->updater->sendError($serviceId, $e->getMessage());
                    $output->writeln($e->getMessage());
                    $hasError = true;
                }
            }

            if ($ready) {
                $this->updater->sendReloadingEnd($serviceId);
                $output->writeln($serviceId . ' updated');
            }
        }

        return Command::SUCCESS;
    }
}
