<?php

namespace App\Command;

use App\Entity\WebHost\WebHost;
use App\Entity\WebHost\WebHostConfiguration\DockerSwarmConfiguration;
use App\Entity\WebHost\WebHostConfiguration\VirtualMachineConfiguration;
use Doctrine\Persistence\ManagerRegistry;
use Doctrine\Persistence\ObjectManager;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name: 'app:update-web-id',
    description: 'Met à jour la colonne web-id',
)]
readonly class UpdateWebIdCommand
{
    private ObjectManager $entityManager;

    public function __construct(
        ManagerRegistry $doctrine,
    ) {
        $this->entityManager = $doctrine->getManager();
    }

    public function __invoke(
        OutputInterface $output
    ): int {
        // boucle sur les web id des webhostsConfiguration et met à jour la colonne web-id en fonction du type

        $webHosts = $this->entityManager->getRepository(WebHost::class)->findAll();
        foreach ($webHosts as $webHost) {
            $configuration = $webHost->getConfiguration();
            if ($configuration instanceof VirtualMachineConfiguration) {
                $configuration->setWebId($configuration->getWebIdFromLocation());
            } elseif ($configuration instanceof DockerSwarmConfiguration) {
                $configuration->setWebId($configuration->getStack());
            }
            $this->entityManager->persist($configuration);
        }
        $this->entityManager->flush();

        $output->writeln('web-id mis à jour');

        return Command::SUCCESS;
    }
}
