<?php

namespace App\Command;

use App\Message\FetchSwarmpitMessage;
use App\Service\SwarmpitAPI;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\Messenger\MessageBusInterface;

#[AsCommand(
    name: 'app:refresh-all-services',
    description: 'Refresh les infos de tous les services',
)]
class RefreshAllServicesCommand extends BaseServicesCommand
{
    public function __construct(
        private readonly SwarmpitAPI $api,
        private readonly MessageBusInterface $bus
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this->addOption('with-details', null, InputOption::VALUE_NONE, 'Récupérer les détails des services');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $withDetails = $input->getOption('with-details');

        $this->api->setNoCache(true);

        $response = $this->api->getAllServicesAsync(!$withDetails);

        if ($withDetails) {
            $services = array_map(fn ($service) => ['serverName' => $service['server'], 'serviceId' => $service['id']], $response['services']);
            $this->bus->dispatch(new FetchSwarmpitMessage($services));
        }

        $io = new SymfonyStyle($input, $output);

        $io->success('Refresh des services');

        return Command::SUCCESS;
    }
}
