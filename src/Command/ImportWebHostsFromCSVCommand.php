<?php

namespace App\Command;

use App\Entity\WebHost\WebHost;
use App\Entity\WebHost\WebHostConfiguration\VirtualMachineConfiguration;
use App\Entity\WebHost\WebHostUrl;
use App\Entity\Website;
use Doctrine\Persistence\ManagerRegistry;
use Doctrine\Persistence\ObjectManager;
use Symfony\Component\Console\Attribute\Argument;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name: 'app:import-webhosts-from-csv',
    description: 'Import des sites depuis un fichier CSV',
)]
class ImportWebHostsFromCSVCommand
{
    private readonly ObjectManager $entityManager;

    public function __construct(
        ManagerRegistry $doctrine,
    ) {
        $this->entityManager = $doctrine->getManager();
    }

    public function __invoke(
        #[Argument(description: 'Chemin du fichier CSV', name: 'filePath')]
        string $filePath,
        OutputInterface $output
    ): int {
        $filePath = $filePath;

        // extraction des données
        $rowNo = 1;
        $headers = [];
        if (($fp = fopen($filePath, 'r')) !== false) {
            while (($row = fgetcsv($fp, 1000, ';', escape: '\\')) !== false) {
                if (1 === $rowNo) {
                    $headers = $row;
                    ++$rowNo;
                    continue;
                }
                $websiteDatas = array_combine($headers, $row);
                $output->writeln(sprintf('Import du site : %s', $websiteDatas['URLs']));
                $this->importWebsite($websiteDatas);

                ++$rowNo;
            }
            fclose($fp);
            $output->writeln('Enregistrement en base de données');
            $this->entityManager->flush();
        }

        return Command::SUCCESS;
    }

    private function importWebsite(array $datas): void
    {
        $urls = explode(',', (string) $datas['URLs']);
        $urls = array_map(function ($url) {
            if ('' === $url) {
                return null;
            }
            $url = trim($url);

            // ajoute le protocole si nécessaire
            return !str_starts_with($url, 'http') ? 'https://' . $url : $url;
        }, array_filter($urls));

        $website = new Website();
        $website->setServer($datas['Server']);
        $website->setProjectDirLocation($datas['Web ID']);
        $website->setUrls($urls);
        $website->setGitlabRemoteUrl($datas['Repository']);
        $website->setGitlabActiveBranch($datas['Branch']);
        $this->entityManager->persist($website);

        $webHost = new WebHost();
        $webHost->setGitlabRemoteUrl($datas['Repository']);
        $webHost->setGitlabActiveBranch($datas['Branch']);

        $configuration = new VirtualMachineConfiguration();
        $configuration->setIp($datas['Server']);
        $configuration->setLocation($datas['Web ID']);
        $webHost->setConfiguration($configuration);

        foreach ($urls as $url) {
            $webHostUrl = new WebHostUrl();
            $webHostUrl->setUrl($url);
            $webHost->addUrl($webHostUrl);
            //            $this->entityManager->persist($webHostUrl);
        }
        $this->entityManager->persist($webHost);
    }
}
