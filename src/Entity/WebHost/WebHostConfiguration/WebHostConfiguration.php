<?php

namespace App\Entity\WebHost\WebHostConfiguration;

use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
#[ORM\InheritanceType('SINGLE_TABLE')]
#[ORM\DiscriminatorColumn(name: 'hostType', type: 'string')]
#[ORM\DiscriminatorMap([
    'vm' => VirtualMachineConfiguration::class,
    'swarm' => DockerSwarmConfiguration::class,
])]
abstract class WebHostConfiguration
{
    public const TYPE_VM = 'vm';
    public const TYPE_SWARM = 'swarm';

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $id = null;

    #[ORM\Column(type: 'string', nullable: true)]
    private ?string $webId = null;

    public function getType(): string
    {
        return '';
    }

    public function getWebId(): ?string
    {
        return $this->webId;
    }

    public function setWebId(?string $webId = null): static
    {
        $this->webId = $webId;

        return $this;
    }
}
