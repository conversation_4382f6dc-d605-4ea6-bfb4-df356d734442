<?php

namespace App\Entity\WebHost\WebHostConfiguration;

use App\Model\DockerSwarmCluster;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
class DockerSwarmConfiguration extends WebHostConfiguration
{
    #[ORM\Column(type: 'string', nullable: true, enumType: DockerSwarmCluster::class)]
    private ?DockerSwarmCluster $clusterName = null;

    #[ORM\Column(type: 'string', nullable: true)]
    private ?string $swarmpitUrl = null;

    #[ORM\Column(type: 'string', nullable: true)]
    private ?string $stack = null;

    #[ORM\Column(type: 'string', nullable: true)]
    private ?string $serviceName = null;

    public function getClusterName(): ?DockerSwarmCluster
    {
        return $this->clusterName;
    }

    public function setClusterName(?DockerSwarmCluster $clusterName): void
    {
        $this->clusterName = $clusterName;
    }

    public function getSwarmpitUrl(): ?string
    {
        return $this->swarmpitUrl;
    }

    public function setSwarmpitUrl(?string $swarmpitUrl): void
    {
        $this->swarmpitUrl = $swarmpitUrl;
    }

    public function getStack(): ?string
    {
        return $this->stack;
    }

    public function setStack(?string $stack): void
    {
        $this->stack = $stack;
    }

    public function getServiceName(): ?string
    {
        return $this->serviceName;
    }

    public function setServiceName(?string $serviceName): void
    {
        $this->serviceName = $serviceName;
    }

    public function getType(): string
    {
        return self::TYPE_SWARM;
    }
}
