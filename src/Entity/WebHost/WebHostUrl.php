<?php

namespace App\Entity\WebHost;

use App\Entity\Report\HealthCheckReport;
use App\Entity\Report\SSLCertificatesReport;
use App\Entity\Timestampable;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
#[ORM\HasLifecycleCallbacks]
class WebHostUrl
{
    use Timestampable;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $id = null;

    #[ORM\Column(type: 'string', nullable: true)]
    private string $url;

    #[ORM\Embedded(class: HealthCheckReport::class, columnPrefix: 'health_report_')]
    private ?HealthCheckReport $healthCheckReport = null;

    #[ORM\Embedded(class: SSLCertificatesReport::class, columnPrefix: 'ssl_report_')]
    private ?SSLCertificatesReport $sslCertificateReport = null;

    #[ORM\ManyToOne(inversedBy: 'urls')]
    #[ORM\JoinColumn(nullable: false)]
    private ?WebHost $webHost = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): void
    {
        $this->id = $id;
    }

    public function getUrl(): string
    {
        return $this->url;
    }

    public function setUrl(string $url): void
    {
        $this->url = $url;
    }

    public function getDomain(): string
    {
        return parse_url($this->url, PHP_URL_HOST);
    }

    public function getHealthCheckReport(): ?HealthCheckReport
    {
        return $this->healthCheckReport;
    }

    public function setHealthCheckReport(?HealthCheckReport $healthCheckReport): void
    {
        $this->healthCheckReport = $healthCheckReport;
    }

    public function getSslCertificateReport(): ?SSLCertificatesReport
    {
        return $this->sslCertificateReport;
    }

    public function setSslCertificateReport(?SSLCertificatesReport $sslCertificateReport): void
    {
        $this->sslCertificateReport = $sslCertificateReport;
    }

    public function getWebHost(): ?WebHost
    {
        return $this->webHost;
    }

    public function setWebHost(?WebHost $webHost): static
    {
        $this->webHost = $webHost;

        return $this;
    }
}
