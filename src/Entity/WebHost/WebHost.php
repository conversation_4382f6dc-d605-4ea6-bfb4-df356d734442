<?php

namespace App\Entity\WebHost;

use App\Entity\Timestampable;
use App\Entity\WebHost\WebHostConfiguration\DockerSwarmConfiguration;
use App\Entity\WebHost\WebHostConfiguration\WebHostConfiguration;
use App\Model\DockerSwarmCluster;
use App\Model\Server;
use App\Model\Swarmpit\SwarmpitService;
use App\Model\WebsiteVisibility;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
#[ORM\HasLifecycleCallbacks]
class WebHost
{
    use Timestampable;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $id = null;

    #[ORM\Column(type: 'string', nullable: true)]
    private ?string $name = null;

    #[ORM\Column(type: 'string', nullable: true, enumType: WebHostEnvironnement::class)]
    private ?WebHostEnvironnement $environnement = null;

    #[ORM\Column(type: 'string', nullable: true, enumType: WebsiteVisibility::class)]
    private ?WebsiteVisibility $expectedVisibility = null;

    #[ORM\Column(type: 'string', nullable: true)]
    private ?string $gitlabRemoteUrl = null;

    #[ORM\Column(type: 'string', nullable: true)]
    private ?string $gitlabActiveBranch = null;

    #[ORM\Column(type: 'string', nullable: true)]
    private ?string $lastCommitDate = null;

    #[ORM\Column(type: 'string', nullable: true)]
    private ?string $confluenceUrl = null;

    #[ORM\Column(type: 'string', nullable: true)]
    private ?string $databaseExplorerUrl = null;

    #[ORM\Column(type: 'json', options: ['default' => '[]'])]
    private array $associatedUrls = [];

    /**
     * @var Collection<int, WebHostUrl>
     */
    #[ORM\OneToMany(mappedBy: 'webHost', targetEntity: WebHostUrl::class, cascade: ['persist', 'remove'], fetch: 'EAGER', orphanRemoval: true)]
    private Collection $urls;

    #[ORM\OneToOne(cascade: ['persist', 'remove'], fetch: 'EAGER')]
    private ?WebHostConfiguration $configuration = null;

    public function __construct()
    {
        $this->urls = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): void
    {
        $this->id = $id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(?string $name): void
    {
        $this->name = $name;
    }

    public function getEnvironnement(): ?WebHostEnvironnement
    {
        return $this->environnement;
    }

    public function setEnvironnement(?WebHostEnvironnement $environnement): void
    {
        $this->environnement = $environnement;
    }

    public function getExpectedVisibility(): ?WebsiteVisibility
    {
        return $this->expectedVisibility;
    }

    public function setExpectedVisibility(?WebsiteVisibility $expectedVisibility): void
    {
        $this->expectedVisibility = $expectedVisibility;
    }

    public function getGitlabRemoteUrl(): ?string
    {
        return $this->gitlabRemoteUrl;
    }

    public function setGitlabRemoteUrl(?string $gitlabRemoteUrl): void
    {
        $this->gitlabRemoteUrl = $gitlabRemoteUrl;
    }

    public function getGitlabActiveBranch(): ?string
    {
        return $this->gitlabActiveBranch;
    }

    public function setGitlabActiveBranch(?string $gitlabActiveBranch): void
    {
        $this->gitlabActiveBranch = $gitlabActiveBranch;
    }

    public function getLastCommitDate(): ?string
    {
        return $this->lastCommitDate;
    }

    public function setLastCommitDate(?string $lastCommitDate): void
    {
        $this->lastCommitDate = $lastCommitDate;
    }

    public function getConfluenceUrl(): ?string
    {
        return $this->confluenceUrl;
    }

    public function setConfluenceUrl(?string $confluenceUrl): void
    {
        $this->confluenceUrl = $confluenceUrl;
    }

    public function getDatabaseExplorerUrl(): ?string
    {
        return $this->databaseExplorerUrl;
    }

    public function setDatabaseExplorerUrl(?string $databaseExplorerUrl): void
    {
        $this->databaseExplorerUrl = $databaseExplorerUrl;
    }

    public function getAssociatedUrls(): array
    {
        return $this->associatedUrls;
    }

    public function setAssociatedUrls(array $associatedUrls): void
    {
        $this->associatedUrls = $associatedUrls;
    }

    /**
     * @return Collection<int, WebHostUrl>
     */
    public function getUrls(): Collection
    {
        return $this->urls;
    }

    public function addUrl(WebHostUrl $url): static
    {
        if (!$this->urls->contains($url)) {
            $this->urls->add($url);
            $url->setWebHost($this);
        }

        return $this;
    }

    public function removeUrl(WebHostUrl $url): static
    {
        if ($this->urls->removeElement($url)) {
            // set the owning side to null (unless already changed)
            if ($url->getWebHost() === $this) {
                $url->setWebHost(null);
            }
        }

        return $this;
    }

    public function getConfiguration(): ?WebHostConfiguration
    {
        return $this->configuration;
    }

    public function setConfiguration(?WebHostConfiguration $configuration): static
    {
        $this->configuration = $configuration;

        return $this;
    }

    /**
     * @param array{urls: array<string>} $formattedService
     */
    public static function fromSwarmpitService(SwarmpitService $service, Server $server, array $formattedService): self
    {
        return self::hydrateFromSwarmpitService(new self(), $service, $server, $formattedService['urls']);
    }

    public function updateFromSwarmpitService(SwarmpitService $service, Server $server, array $formattedService)
    {
        return self::hydrateFromSwarmpitService($this, $service, $server, $formattedService['urls']);
    }

    public static function hydrateFromSwarmpitService(WebHost $webHost, SwarmpitService $service, Server $server, $urls): static
    {
        if ($service->hasRemoteDetails() && isset($service->details)) {
            $details = $service->details;
            $webHost->setGitlabRemoteUrl($details['git']['url'] ?? null);
            $webHost->setGitlabActiveBranch($details['git']['branch'] ?? null);
            $webHost->setLastCommitDate($details['git']['history'][0]['date'] ?? null);
        }
        $webHost->setEnvironnement(WebHostEnvironnement::fromServer($server));

        $configuration = $webHost->getConfiguration();
        if (!$configuration instanceof DockerSwarmConfiguration) {
            $webHost->setConfiguration(new DockerSwarmConfiguration());
        }
        $configuration->setWebId($service->stack);
        $configuration->setClusterName(DockerSwarmCluster::tryFrom($server->name));
        $configuration->setSwarmpitUrl($server->url);
        $configuration->setStack($service->stack);
        $configuration->setServiceName($service->serviceName);

        // suppression des URLs qui n'existent plus
        foreach ($webHost->getUrls() as $webHostUrl) {
            if (!in_array($webHostUrl->getUrl(), $urls, true)) {
                $webHost->removeUrl($webHostUrl);
            }
        }
        // ajout des URLs s'ils n'existent pas déjà
        $existingUrls = $webHost->getUrls()->map(fn (WebHostUrl $webHostUrl) => $webHostUrl->getUrl())->toArray();
        foreach ($urls as $url) {
            if (!in_array($url, $existingUrls, true)) {
                $webHostUrl = new WebHostUrl();
                $webHostUrl->setUrl($url);
                $webHost->addUrl($webHostUrl);
            }
        }

        return $webHost;
    }
}
