<?php

namespace App\Entity\WebHost;

use App\Model\Server;

enum WebHostEnvironnement: string
{
    case DEV = 'dev';
    case PREPROD = 'preprod';
    case PROD = 'prod';

    public static function fromServer(Server $server): ?self
    {
        $env = $server->env;
        if (in_array($env, ['prod', 'production'])) {
            return self::PROD;
        }

        return self::tryFrom($server->env);
    }
}
