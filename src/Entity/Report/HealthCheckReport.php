<?php

namespace App\Entity\Report;

use App\Service\HealthCheck\HealthStatus;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Embeddable]
class HealthCheckReport
{
    public function __construct(
        #[ORM\Column(type: 'string', nullable: true, enumType: HealthStatus::class)]
        public ?HealthStatus $status,

        #[ORM\Column(type: 'string', nullable: true)]
        public ?string $url,

        #[ORM\Column(type: 'integer', nullable: true)]
        public ?int $statusCode,
    ) {
    }
}
