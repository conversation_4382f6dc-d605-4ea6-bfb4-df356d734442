<?php

namespace App\Entity\Report;

use App\Service\SSLCertificate\SSLCertificateStatus;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Embeddable]
class SSLCertificatesReport
{
    public function __construct(
        #[ORM\Column(type: 'json', nullable: true)]
        /** @var SSLCertificateStatus[] */
        public ?array $statuses,

        #[ORM\Column(type: 'boolean', nullable: true)]
        public ?bool $isValid = null,
    ) {
    }
}
