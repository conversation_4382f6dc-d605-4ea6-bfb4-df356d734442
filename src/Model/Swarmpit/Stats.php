<?php

namespace App\Model\Swarmpit;

final class Stats
{
    public ?int $memoryLimit = null;
    public ?int $memory = null;
    public ?float $cpuPercentage = null;
    public ?float $cpuLimit = null;
    public ?float $memoryPercentage = null;
    public ?float $cpu = null;

    public static function fromJson(array $data): self
    {
        $instance = new self();
        $instance->memoryLimit = $data['memoryLimit'] ?? null;
        $instance->memory = $data['memory'] ?? null;
        $instance->cpuPercentage = $data['cpuPercentage'] ?? null;
        $instance->cpuLimit = $data['cpuLimit'] ?? null;
        $instance->memoryPercentage = $data['memoryPercentage'] ?? null;
        $instance->cpu = $data['cpu'] ?? null;

        return $instance;
    }
}
