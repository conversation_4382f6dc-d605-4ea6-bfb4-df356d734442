<?php

namespace App\Model\Swarmpit;

final class RestartPolicy
{
    public ?string $condition = null;
    public ?int $delay = null;
    public ?int $window = null;
    public ?int $attempts = null;

    public static function fromJson(array $data): self
    {
        $instance = new self();
        $instance->condition = $data['condition'] ?? null;
        $instance->delay = $data['delay'] ?? null;
        $instance->window = $data['window'] ?? null;
        $instance->attempts = $data['attempts'] ?? null;

        return $instance;
    }
}
