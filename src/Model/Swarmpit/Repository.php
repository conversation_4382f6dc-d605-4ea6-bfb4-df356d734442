<?php

namespace App\Model\Swarmpit;

final class Repository
{
    public ?string $name = null;
    public ?string $tag = null;
    public ?string $image = null;
    public ?string $imageDigest = null;

    public static function fromJson(array $data): self
    {
        $instance = new self();
        $instance->name = $data['name'] ?? null;
        $instance->tag = $data['tag'] ?? null;
        $instance->image = $data['image'] ?? null;
        $instance->imageDigest = $data['imageDigest'] ?? null;

        return $instance;
    }
}
