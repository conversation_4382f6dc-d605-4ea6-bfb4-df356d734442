<?php

namespace App\Model\Swarmpit;

final class Update
{
    public ?int $parallelism = null;
    public ?int $delay = null;
    public ?string $order = null;
    public ?string $failureAction = null;

    public static function fromJson(array $data): self
    {
        $instance = new self();
        $instance->parallelism = $data['parallelism'] ?? null;
        $instance->delay = $data['delay'] ?? null;
        $instance->order = $data['order'] ?? null;
        $instance->failureAction = $data['failureAction'] ?? null;

        return $instance;
    }
}
