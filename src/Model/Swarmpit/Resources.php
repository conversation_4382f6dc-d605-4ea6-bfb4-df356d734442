<?php

namespace App\Model\Swarmpit;

final class Resources
{
    public ?Reservation $reservation = null;
    public ?Limit $limit = null;

    public static function from<PERSON>son(array $data): self
    {
        $instance = new self();
        $instance->reservation =
            ($data['reservation'] ?? null) !== null
                ? Reservation::from<PERSON>son($data['reservation'])
                : null;
        $instance->limit =
            ($data['limit'] ?? null) !== null
                ? Limit::from<PERSON>son($data['limit'])
                : null;

        return $instance;
    }
}
