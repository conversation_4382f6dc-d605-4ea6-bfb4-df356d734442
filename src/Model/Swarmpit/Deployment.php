<?php

namespace App\Model\Swarmpit;

final class Deployment
{
    public ?Update $update = null;
    public ?int $forceUpdate = null;
    public ?RestartPolicy $restartPolicy = null;
    public ?Rollback $rollback = null;
    public ?bool $rollbackAllowed = null;
    public ?bool $autoredeploy = null;
    /** @var Placement[]|null */
    public ?array $placement = null;

    public static function fromJson(array $data): self
    {
        $instance = new self();
        $instance->update =
            ($data['update'] ?? null) !== null
                ? Update::fromJson($data['update'])
                : null;
        $instance->forceUpdate = $data['forceUpdate'] ?? null;
        $instance->restartPolicy =
            ($data['restartPolicy'] ?? null) !== null
                ? RestartPolicy::fromJson($data['restartPolicy'])
                : null;
        $instance->rollback =
            ($data['rollback'] ?? null) !== null
                ? Rollback::fromJson($data['rollback'])
                : null;
        $instance->rollbackAllowed = $data['rollbackAllowed'] ?? null;
        $instance->autoredeploy = $data['autoredeploy'] ?? null;
        $instance->placement =
            ($data['placement'] ?? null) !== null
                ? array_map(static function ($data) {
                    return Placement::fromJson($data);
                }, $data['placement'])
                : null;

        return $instance;
    }
}
