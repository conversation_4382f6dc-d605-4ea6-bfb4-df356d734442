<?php

namespace App\Model\Swarmpit;

final class SwarmpitService
{
    public const REDEPLOY_ALLOWED_IMAGE_PREFIX = ['web_php'];

    public ?string $id = null;
    public ?int $version = null;
    public ?string $createdAt = null;
    public ?string $updatedAt = null;
    public ?Repository $repository = null;
    public ?string $serviceName = null;
    public ?string $mode = null;
    public ?string $stack = null;
    public ?string $agent = null;
    public ?string $immutable = null;
    public ?array $links = null;
    public ?int $replicas = null;
    public ?string $state = null;
    public ?Status $status = null;
    public ?array $ports = null;
    /** @var Mounts[]|null */
    public ?array $mounts = null;
    /** @var Networks[]|null */
    public ?array $networks = null;
    public ?array $secrets = null;
    public ?array $configs = null;
    public ?array $hosts = null;
    public ?array $variables = null;
    /** @var Labels[]|null */
    public ?array $labels = null;
    public ?array $containerLabels = null;
    public ?array $command = null;
    public ?string $user = null;
    public ?string $dir = null;
    public ?string $tty = null;
    public ?array $healthcheck = null;
    public ?Logdriver $logdriver = null;
    public ?Resources $resources = null;
    public ?Deployment $deployment = null;
    /** @var SwarmpitTask[]|null */
    public ?array $tasks = null;
    public ?array $details = null;

    public function isFrontWeb(): bool
    {
        return 'front_web' === str_replace($this->stack . '_', '', $this->serviceName);
    }

    public function isAllowedToRedeploy(): bool
    {
        if ('web-default' === $this->stack) {
            return false;
        }

        foreach (self::REDEPLOY_ALLOWED_IMAGE_PREFIX as $prefix) {
            if (str_contains($this->repository->image, $prefix)) {
                return true;
            }
        }

        return $this->isFrontWeb();
    }

    public function hasRemoteDetails(): bool
    {
        return $this->isAllowedToRedeploy();
    }

    public static function fromJson(array $data): self
    {
        $instance = new self();
        $instance->id = $data['id'] ?? null;
        $instance->version = $data['version'] ?? null;
        $instance->createdAt = $data['createdAt'] ?? null;
        $instance->updatedAt = $data['updatedAt'] ?? null;
        $instance->repository =
            ($data['repository'] ?? null) !== null
                ? Repository::fromJson($data['repository'])
                : null;
        $instance->serviceName = $data['serviceName'] ?? null;
        $instance->mode = $data['mode'] ?? null;
        $instance->stack = $data['stack'] ?? null;
        $instance->agent = $data['agent'] ?? null;
        $instance->immutable = $data['immutable'] ?? null;
        $instance->links = $data['links'] ?? null;
        $instance->replicas = $data['replicas'] ?? null;
        $instance->state = $data['state'] ?? null;
        $instance->status =
            ($data['status'] ?? null) !== null
                ? Status::fromJson($data['status'])
                : null;
        $instance->ports = $data['ports'] ?? null;
        $instance->mounts =
            ($data['mounts'] ?? null) !== null
                ? array_map(static function ($data) {
                    return Mounts::fromJson($data);
                }, $data['mounts'])
                : null;
        $instance->networks =
            ($data['networks'] ?? null) !== null
                ? array_map(static function ($data) {
                    return Networks::fromJson($data);
                }, $data['networks'])
                : null;
        $instance->secrets = $data['secrets'] ?? null;
        $instance->configs = $data['configs'] ?? null;
        $instance->hosts = $data['hosts'] ?? null;
        $instance->variables = $data['variables'] ?? null;
        $instance->labels =
            ($data['labels'] ?? null) !== null
                ? array_map(static function ($data) {
                    return Labels::fromJson($data);
                }, $data['labels'])
                : null;
        $instance->containerLabels = $data['containerLabels'] ?? null;
        $instance->command = $data['command'] ?? null;
        $instance->user = $data['user'] ?? null;
        $instance->dir = $data['dir'] ?? null;
        $instance->tty = $data['tty'] ?? null;
        $instance->healthcheck = $data['healthcheck'] ?? null;
        $instance->logdriver =
            ($data['logdriver'] ?? null) !== null
                ? Logdriver::fromJson($data['logdriver'])
                : null;
        $instance->resources =
            ($data['resources'] ?? null) !== null
                ? Resources::fromJson($data['resources'])
                : null;
        $instance->deployment =
            ($data['deployment'] ?? null) !== null
                ? Deployment::fromJson($data['deployment'])
                : null;

        return $instance;
    }
}
