<?php

namespace App\Model\Swarmpit;

final class Status
{
    public ?Tasks $tasks = null;
    public ?string $update = null;
    public ?string $message = null;

    public static function from<PERSON>son(array $data): self
    {
        $instance = new self();
        $instance->tasks =
            ($data['tasks'] ?? null) !== null
                ? Tasks::from<PERSON>son($data['tasks'])
                : null;
        $instance->update = $data['update'] ?? null;
        $instance->message = $data['message'] ?? null;

        return $instance;
    }
}
