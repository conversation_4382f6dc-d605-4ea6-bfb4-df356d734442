<?php

namespace App\Model\Swarmpit;

final class Mounts
{
    public ?string $containerPath = null;
    public ?string $host = null;
    public ?string $type = null;
    public ?string $id = null;
    public ?array $volumeOptions = null;
    public ?bool $readOnly = null;
    public ?string $stack = null;

    public static function fromJson(array $data): self
    {
        $instance = new self();
        $instance->containerPath = $data['containerPath'] ?? null;
        $instance->host = $data['host'] ?? null;
        $instance->type = $data['type'] ?? null;
        $instance->id = $data['id'] ?? null;
        $instance->volumeOptions = $data['volumeOptions'] ?? null;
        $instance->readOnly = $data['readOnly'] ?? null;
        $instance->stack = $data['stack'] ?? null;

        return $instance;
    }
}
