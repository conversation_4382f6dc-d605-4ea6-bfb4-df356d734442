<?php

namespace App\Model\Swarmpit;

final class SwarmpitTask
{
    public ?string $updatedAt = null;
    public ?Repository $repository = null;
    public ?string $nodeId = null;
    public ?string $createdAt = null;
    public ?string $state = null;
    public ?string $desiredState = null;
    public ?string $logdriver = null;
    public ?TaskStatus $status = null;
    public ?string $id = null;
    public ?string $nodeName = null;
    public ?string $serviceName = null;
    public ?int $version = null;
    public ?Resources $resources = null;
    public ?string $taskName = null;
    public ?Stats $stats = null;

    public static function fromJson(array $data): self
    {
        $instance = new self();
        $instance->updatedAt = $data['updatedAt'] ?? null;
        $instance->repository = ($data['repository'] ?? null) !== null ? Repository::fromJson($data['repository']) : null;
        $instance->nodeId = $data['nodeId'] ?? null;
        $instance->createdAt = $data['createdAt'] ?? null;
        $instance->state = $data['state'] ?? null;
        $instance->desiredState = $data['desiredState'] ?? null;
        $instance->logdriver = $data['logdriver'] ?? null;
        $instance->status = ($data['status'] ?? null) !== null ? TaskStatus::fromJson($data['status']) : null;
        $instance->id = $data['id'] ?? null;
        $instance->nodeName = $data['nodeName'] ?? null;
        $instance->serviceName = $data['serviceName'] ?? null;
        $instance->version = $data['version'] ?? null;
        $instance->resources = ($data['resources'] ?? null) !== null ? Resources::fromJson($data['resources']) : null;
        $instance->taskName = $data['taskName'] ?? null;
        $instance->stats = ($data['stats'] ?? null) !== null ? Stats::fromJson($data['stats']) : null;

        return $instance;
    }
}
