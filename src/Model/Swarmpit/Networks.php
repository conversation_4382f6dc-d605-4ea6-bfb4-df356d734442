<?php

namespace App\Model\Swarmpit;

final class Networks
{
    public ?NetworkLabels $labels = null;
    public ?bool $ingress = null;
    public ?bool $enableIPv6 = null;
    public ?string $created = null;
    public ?string $scope = null;
    public ?bool $internal = null;
    public ?string $id = null;
    public ?Ipam $ipam = null;
    public ?string $stack = null;
    /** @var Options[]|null */
    public ?array $options = null;
    public ?string $networkName = null;
    public ?string $driver = null;
    public ?bool $attachable = null;
    /** @var string[]|null */
    public ?array $serviceAliases = null;

    public static function fromJson(array $data): self
    {
        $instance = new self();
        $instance->labels =
            ($data['labels'] ?? null) !== null
                ? NetworkLabels::fromJson($data['labels'])
                : null;
        $instance->ingress = $data['ingress'] ?? null;
        $instance->enableIPv6 = $data['enableIPv6'] ?? null;
        $instance->created = $data['created'] ?? null;
        $instance->scope = $data['scope'] ?? null;
        $instance->internal = $data['internal'] ?? null;
        $instance->id = $data['id'] ?? null;
        $instance->ipam =
            ($data['ipam'] ?? null) !== null
                ? Ipam::fromJson($data['ipam'])
                : null;
        $instance->stack = $data['stack'] ?? null;
        $instance->options =
            ($data['options'] ?? null) !== null
                ? array_map(static function ($data) {
                    return Options::fromJson($data);
                }, $data['options'])
                : null;
        $instance->networkName = $data['networkName'] ?? null;
        $instance->driver = $data['driver'] ?? null;
        $instance->attachable = $data['attachable'] ?? null;
        $instance->serviceAliases = $data['serviceAliases'] ?? null;

        return $instance;
    }
}
