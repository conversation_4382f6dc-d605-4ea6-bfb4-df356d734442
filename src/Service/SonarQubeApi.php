<?php

namespace App\Service;

use Psr\Cache\CacheItemPoolInterface;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class SonarQubeApi
{
    public const API_TAGS = '%s/tags/list';

    public const BASE_URL = 'https://sonarqube.int.alienor.net';
    public const API_URL = self::BASE_URL . '/api/';

    public const BADGE_TOKEN_API = 'project_badges/token';
    public const BADGE_URL = self::API_URL . 'project_badges/measure?project=%s&metric=%s&token=%s';

    public const BADGES = [
        'bugs', 'code_smells', 'coverage', 'duplicated_lines_density', 'ncloc', 'sqale_rating', 'alert_status',
        'reliability_rating', 'security_hotspots', 'security_rating', 'sqale_index', 'vulnerabilities',
    ];

    private readonly ApiCache $client;

    /** @var ApiCache[] */
    public function __construct(
        HttpClientInterface $client,
        CacheItemPoolInterface $swarmpitCache,
        #[Autowire(env: 'SONARQUBE_API_KEY')] protected string $sonarQubeApiKey
    ) {
        $this->client = (new ApiCache($client, $swarmpitCache))->withOptions([
            'base_uri' => self::API_URL,
            'auth_basic' => "$sonarQubeApiKey:",
        ]);
    }

    public function getProjectUrl(string $projectKey): string
    {
        return sprintf('%s/dashboard?id=%s', self::BASE_URL, $projectKey);
    }

    public function getBadgeToken(string $projectKey): ?string
    {
        $response = $this->client->request('GET', self::BADGE_TOKEN_API, [
            'query' => ['project' => $projectKey],
        ]);

        return $response->toArray()['token'] ?? null;
    }

    public function getBadges(string $projectKey): array
    {
        $token = $this->getBadgeToken($projectKey);

        return array_combine(
            self::BADGES,
            array_map(fn ($metric) => $this->getBadge($projectKey, $metric, $token), self::BADGES)
        );
    }

    /**
     * Parcours tous les serveurs pour récupérer la liste des tags d'une image.
     */
    public function getBadge(string $projectKey, string $metric, ?string $token = null): string
    {
        if (null === $token) {
            $token = $this->getBadgeToken($projectKey);
        }

        return sprintf(
            self::BADGE_URL,
            $projectKey,
            $metric,
            $token
        );
    }
}
