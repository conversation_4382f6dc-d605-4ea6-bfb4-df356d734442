<?php

namespace App\Service\SSLCertificate;

use App\Entity\Report\SSLCertificatesReport;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Contracts\HttpClient\ResponseInterface;

class SSLCertificateChecker
{
    public function __construct(
        private readonly HttpClientInterface $httpClient,
    ) {
    }

    public function __invoke(string $url): SSLCertificatesReport
    {
        dump($url);
        // TODO gérer les exceptions pour conserver le maxiumum d'informations

        // remplacement de http par https si ce nécessaire
        if (str_starts_with($url, 'http://')) {
            $url = 'https://' . substr($url, 7);
        }
        // ajout de https si aucun protocole n'est spécifié
        if (!str_contains($url, '://')) {
            $url = 'https://' . $url;
        }

        $statuses = $this->call($url);

        $isValid = array_reduce($statuses, fn ($carry, $status) => $carry && false !== $status->isValid, true);

        return new SSLCertificatesReport(
            isValid: $isValid,
            statuses: $statuses
        );
    }

    private function call(string $url, $statuses = []): array
    {
        try {
            $response = $this->httpClient->request('GET', $url, [
                'timeout' => 10,
                'verify_peer' => false, // Vérification SSL désactivée
                'capture_peer_cert_chain' => true, // Capture des certificats SSL
                'max_redirects' => 0,
            ]);
            $response->getStatusCode(); // Déclenchement de la requête
        } catch (TransportExceptionInterface $e) {
            $currentUrlStatus = new SSLCertificateStatus(
                url: $url,
                exceptionMessage: $e->getMessage(),
            );

            return array_merge([$currentUrlStatus], $statuses);
        }

        $certificateStatus = $this->getSSLCertificateStatus($response);

        $redirectUrl = $response->getHeaders(false)['location'][0] ?? null;
        if (null !== $redirectUrl) {
            $isValidUrl = false !== filter_var($redirectUrl, FILTER_VALIDATE_URL);
            $currentUrlDomain = parse_url($url, PHP_URL_HOST);
            $redirectUrlDomain = parse_url($redirectUrl, PHP_URL_HOST);
            $isSameDomain = $currentUrlDomain === $redirectUrlDomain;
            if ($isValidUrl && !$isSameDomain) {
                $statuses = array_merge($statuses, $this->call($redirectUrl, $statuses));
            }
        }

        return array_merge([$certificateStatus], $statuses);
    }

    private function getSSLCertificateStatus(ResponseInterface $response): SSLCertificateStatus
    {
        $info = $response->getInfo();
        $domain = parse_url((string) $info['original_url'])['host'];

        $isValid = false;
        $creationDate = null;
        $expirationDate = null;

        // recherche du certificat SSL correspondant au domaine
        $certificate = null;
        if (isset($info['peer_certificate_chain'])) {
            $certificates = array_map('openssl_x509_parse', $info['peer_certificate_chain']);
            $certificate = $certificates[0] ?? null;
        }

        // récupération des informations du certificat SSL
        if (null !== $certificate) {
            $creationDate = \DateTimeImmutable::createFromFormat('U', $certificate['validFrom_time_t']);
            $expirationDate = \DateTimeImmutable::createFromFormat('U', $certificate['validTo_time_t']);
            $now = new \DateTimeImmutable();
            $isValid = $now > $creationDate && $expirationDate > $now;
        }

        return new SSLCertificateStatus(
            url: $info['url'],
            isValid: $isValid,
            creationDate: $creationDate,
            expirationDate: $expirationDate,
        );
    }
}
