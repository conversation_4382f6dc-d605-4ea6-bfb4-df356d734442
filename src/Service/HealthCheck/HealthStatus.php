<?php

namespace App\Service\HealthCheck;

enum HealthStatus: string
{
    case OK = 'OK';
    case WARNING = 'WARNING';
    case CRITICAL = 'CRITICAL';

    public static function fromStatusCode(int $statusCode): self
    {
        if ($statusCode >= 200 && $statusCode < 400) {
            return self::OK;
        }

        if ($statusCode >= 400 && $statusCode < 500) {
            return self::WARNING;
        }

        return self::CRITICAL;
    }
}
