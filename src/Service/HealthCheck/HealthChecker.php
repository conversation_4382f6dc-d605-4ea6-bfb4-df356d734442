<?php

namespace App\Service\HealthCheck;

use App\Entity\Report\HealthCheckReport;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;

readonly class HealthChecker
{
    public function __construct(
        private HttpClientInterface $httpClient,
    ) {
    }

    public function __invoke(string $url): HealthCheckReport
    {
        try {
            dump($url);
            $response = $this->httpClient->request('GET', $url, [
                'timeout' => 10,
                'verify_peer' => false, // Vérification SSL désactivée, celle-ci est contrôlée dans un second temps
            ]);
            $statusCode = $response->getStatusCode();
        } catch (TransportExceptionInterface $e) {
            $statusCode = $e->getCode();
        }

        return new HealthCheckReport(
            status: HealthStatus::fromStatusCode($statusCode),
            url: $url,
            statusCode: $statusCode,
        );
    }
}
