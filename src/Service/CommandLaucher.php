<?php

namespace App\Service;

use App\Command\BaseServicesCommand;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\Process\Process;

class CommandLaucher
{
    public function __construct(
        #[Autowire(param: 'kernel.project_dir')] private readonly string $projectDir,
    ) {
    }

    /**
     * @param class-string<BaseServicesCommand> $commandClass
     */
    public function runAsyncCommand(string $commandClass, array $services, array $options = [], $async = true): Process
    {
        $process = $commandClass::getAsProcess($services, $this->projectDir, $options, $async);
        $process->run();

        return $process;
    }
}
