<?php

namespace App\Service;

use App\Model\Server;
use App\Model\Swarmpit\SwarmpitService;

class DockerTools
{
    public static function getDockerToolsCommand(Server $server, SwarmpitService $service)
    {
        $serverName = self::getServerName($server);

        return sprintf('dockertools://open?action=run&server=%s&service=%s', $serverName, $service->serviceName);
    }

    private static function getServerName(Server $server)
    {
        return match ($server->name) {
            'anet-dev' => 'anet-dev',
            'spp.aquitem.fr' => 'aquitem-preprod-old',
            'sp.aquitem.fr' => 'aquitem-prod-old',
            'spp.alienor.net' => 'alienor-preprod',
            'sp.alienor.net' => 'alienor-prod',
            'aqui-swarm-p' => 'aquitem-prod',
            'aqui-swarm-pp' => 'aquitem-preprod',
            'datalake2' => 'datalake',
            'lascaux' => 'lascaux',
            default => $server->name,
        };
    }
}
