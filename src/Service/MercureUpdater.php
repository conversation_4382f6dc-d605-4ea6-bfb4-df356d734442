<?php

namespace App\Service;

use App\Entity\WebHost\WebHost;
use Symfony\Component\Mercure\HubInterface;
use Symfony\Component\Mercure\Update;
use Symfony\Component\Serializer\SerializerInterface;

class MercureUpdater
{
    public function __construct(
        private readonly HubInterface $hub,
        private readonly SerializerInterface $serializer
    ) {
    }

    public function updateService(array $service)
    {
        $this->publish('service', $service);
    }

    public function sendError(string $serviceId, string $message)
    {
        $this->publish('service.error', [
            'id' => $serviceId,
            'exception' => $message,
        ]);
    }

    public function sendReloadingEnd(string $serviceId)
    {
        $this->publish('service.reloading', [
            'id' => $serviceId,
        ]);
    }

    public function updateWebHost(WebHost $webHost)
    {
        $this->publish('webhost.updated', $this->serializer->normalize($webHost));
    }

    private static function createUpdate(string $topic, $data)
    {
        return new Update(
            $topic,
            json_encode([
                'topic' => $topic,
                'data' => $data,
            ])
        );
    }

    private function publish(string $topic, $data)
    {
        $this->hub->publish(self::createUpdate($topic, $data));
    }
}
