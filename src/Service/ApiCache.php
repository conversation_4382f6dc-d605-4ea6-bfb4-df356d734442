<?php

namespace App\Service;

use Psr\Cache\CacheItemPoolInterface;
use Symfony\Component\HttpClient\Response\MockResponse;
use Symfony\Contracts\Cache\ItemInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Contracts\HttpClient\ResponseInterface;
use Symfony\Contracts\HttpClient\ResponseStreamInterface;

class ApiCache
{
    public function __construct(
        private readonly HttpClientInterface $client,
        public CacheItemPoolInterface $swarmpitCache,
        public readonly string $baseUri = '',
    ) {
    }

    /*** Extension des fonctions de base HttpClient ***/

    public function request(string $method, string $url, array $options = [], ?string $cacheKey = null, bool $force = false, $expiresAfter = 86400): ResponseInterface
    {
        if (null === $cacheKey) {
            $cacheKey = $this->getDefaultCacheKey($method, $url, $options);
        }

        $response = $this->swarmpitCache->get($cacheKey, function (ItemInterface $item) use ($method, $url, $options, $expiresAfter) {
            //            dd('no cache');

            return $this->handleRequest($item, $method, $url, $options, $expiresAfter);
        }, $force ? INF : 0);

        return $this->mockResponse($response, $method, $url, $options);
    }

    public function stream(iterable|ResponseInterface $responses, ?float $timeout = null): ResponseStreamInterface
    {
        return $this->client->stream($responses, $timeout);
    }

    public function setCacheResponse(string $cacheKey, array $response): array
    {
        return $this->swarmpitCache->get($cacheKey, function (ItemInterface $item) use ($response) {
            $item->expiresAfter(86400 * 365);

            return $response;
        });
    }

    public function withOptions(array $options): static
    {
        return new self($this->client->withOptions($options), $this->swarmpitCache, $options['base_uri'] ?? '');
    }

    public function handleRequest(ItemInterface $item, string $method, string $url, array $options = [], $expiresAfter = 86400)
    {
        $response = $this->client->request($method, $url, $options);
        if (200 === $response->getStatusCode()) {
            $item->expiresAfter($expiresAfter);
        }

        return $this->serializeResponse($response);
    }

    /*** Système de cache ***/

    public function getDefaultCacheKey(string $method, string $url, array $options = [])
    {
        return md5(sprintf('%s_%s_%s_%s', $this->baseUri, $method, $url, json_encode($options)));
    }

    public function hasCachedItem(string $cacheKey)
    {
        return $this->swarmpitCache->hasItem($cacheKey);
    }

    /**
     * Transforme la réponse du HttpClient de manière à la sauvegardé dans le cache.
     */
    public function serializeResponse(ResponseInterface $response)
    {
        return [
            'content' => $response->getContent(),
            'http_code' => $response->getStatusCode(),
            'response_headers' => $response->getHeaders(),
        ];
    }

    /**
     * Transorme le résultat de requête mis en cache en réponse du HttpClient.
     */
    public function mockResponse(array $response, string $method, string $url, array $options = [])
    {
        $response = new MockResponse(
            $response['content'], [
                'http_code' => $response['http_code'],
                'response_headers' => $response['response_headers'],
            ]
        );

        return MockResponse::fromRequest($method, $url, $options, $response);
    }
}
