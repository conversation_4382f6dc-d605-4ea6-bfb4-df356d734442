<?php

namespace App\Service;

use Psr\Cache\CacheItemPoolInterface;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class RegistryAPI
{
    public const GITLAB_SERVER = 'gitlab.alienor.net';
    public const REGISTRY_URL_GITLAB = self::GITLAB_SERVER . ':5050/';
    public const REGISTRY_API_GITLAB = 'https://' . self::GITLAB_SERVER . '/api/v4/';
    public const REGISTRY_API_ROUTE_GITLAB = 'projects/%s/registry/repositories?tags=true&per_page=100';

    private $client;
    private bool $noCache = true;

    public function __construct(
        HttpClientInterface $client,
        CacheItemPoolInterface $gitlabCache,
        #[Autowire(env: 'GITLAB_API_KEY')] protected string $gitlabApiKey
    ) {
        $this->client = new ApiCache($client, $gitlabCache);
    }

    public function getClientGitlab(): ApiCache
    {
        return $this->client->withOptions([
            'base_uri' => self::REGISTRY_API_GITLAB,
            'headers' => [
                'Authorization' => 'Bearer ' . $this->gitlabApiKey,
            ],
        ]);
    }

    /**
     * Parcours tous les serveurs pour récupérer la liste des tags d'une image.
     */
    public function getTags($image): array
    {
        $tags = [];
        $errors = [];

        try {
            if (str_contains((string) $image, self::GITLAB_SERVER)) {
                $server = self::GITLAB_SERVER;
                $tags = $this->fetchGitlabTags($image);
            }
        } catch (\Exception $e) {
            $errors[] = [
                'server' => $server,
                'exception' => $e->getMessage(),
            ];
        }

        sort($tags);

        return [
            'errors' => $errors,
            'tags' => $tags,
        ];
    }

    public function fetchGitlabTags(string $image): array
    {
        $client = $this->getClientGitlab();
        $project = str_replace(self::REGISTRY_URL_GITLAB, '', $image);
        // On récupère le nom du projet gitlab à partir de l'image, on vire ce qu'il y a après le 2em slash au cas ou c'est une sous-image
        if (substr_count($project, '/') > 1) {
            $project = substr($project, 0, strrpos($project, '/'));
        }
        $project = urlencode($project);
        $response = $client->request('GET', sprintf(self::REGISTRY_API_ROUTE_GITLAB, $project), force: $this->noCache);

        $repositories = $response->toArray();

        $tags = [];
        $key = array_search($image, array_column($repositories, 'location'));
        if (-1 !== $key) {
            $tags = array_column($repositories[$key]['tags'], 'name');
        }

        return $tags;
    }
}
