<?php

namespace App\Service\Favicon;

use Psr\Cache\CacheItemPoolInterface;
use Symfony\Component\DomCrawler\Crawler;
use Symfony\Component\DomCrawler\UriResolver;
use Symfony\Contracts\Cache\ItemInterface;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;

readonly class FaviconRetriever
{
    public function __construct(
        private HttpClientInterface $client,
        private CacheItemPoolInterface $faviconCache,
    ) {
    }

    public function __invoke(string $url): ?string
    {
        $domain = $this->getFullDomain($url);

        return $this->faviconCache->get(base64_encode($domain), function (ItemInterface $item) use ($url, $domain) {
            $item->expiresAfter(86400);

            return $this->tryRetrieveUrlFromDOM($url) ?? $this->tryRetrieveUrlFromUsualPath($domain);
        });
    }

    private function getFullDomain(string $url): ?string
    {
        $parsed = parse_url($url);

        if (!isset($parsed['host'])) {
            return null;
        }

        $host = $parsed['host'];
        $port = isset($parsed['port']) ? ':' . $parsed['port'] : '';

        return "{$host}{$port}";
    }

    private function tryRetrieveUrlFromDOM(string $url): ?string
    {
        try {
            $request = $this->client->request('GET', $url, [
                'timeout' => 3,
                'verify_peer' => false,
            ]);

            $html = $request->getContent();
            if (empty($html)) {
                return null;
            }
        } catch (TransportExceptionInterface|ClientExceptionInterface|RedirectionExceptionInterface|ServerExceptionInterface $e) {
            return null;
        }

        $crawler = new Crawler($html, $url);
        $linkNodes = $crawler->filter('link[rel~="icon"]');

        foreach ($linkNodes as $linkNode) {
            $href = $linkNode->getAttribute('href');

            if (null !== $crawler->getBaseHref()) {
                $resolvedUrl = UriResolver::resolve($href, $crawler->getBaseHref());
            } else {
                $resolvedUrl = UriResolver::resolve($href, $url);
            }
            if ($this->checkUrlExists($resolvedUrl)) {
                return $resolvedUrl;
            }
        }

        return null;
    }

    private function checkUrlExists(string $url): bool
    {
        try {
            $response = $this->client->request('HEAD', $url, [
                'timeout' => 3,
                'verify_peer' => false,
            ]);

            return 200 === $response->getStatusCode();
        } catch (\Throwable $e) {
            return false;
        }
    }

    private function tryRetrieveUrlFromUsualPath(?string $domain): ?string
    {
        $usualPaths = [
            '/favicon.ico',
            '/favicon.png',
        ];

        foreach ($usualPaths as $path) {
            $absoluteUrl = sprintf('http://%s%s', $domain, $path);
            if ($this->checkUrlExists($absoluteUrl)) {
                return $absoluteUrl;
            }
        }

        return null;
    }
}
