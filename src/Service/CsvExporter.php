<?php

namespace App\Service;

class CsvExporter
{
    public function __construct(
        private readonly SwarmpitAPI $api
    ) {
    }

    public function generateCsv($ids = [])
    {
        $services = $this->api->getAllServices()['services'];

        if (count($ids)) {
            $services = array_values(array_filter($services, fn ($s) => in_array($s['id'], $ids)));
        }

        $rows = [[
            'Environnement',
            'Cluster',
            'Stack',
            'Service',
            'Urls',
            'htaccess',
            'Image',
            'Hash',
            'PHP version',
            'Symfony version',
            'Sécurité',
            'PHP Build date',
            'Date de déploiement',
            'Projet git',
            'Branche git',
        ]];
        foreach ($services as $service) {
            $security = null;

            if (isset($service['details']['local-php-security-checker'])) {
                $security = 0;
                foreach ($service['details']['local-php-security-checker'] as $package) {
                    $security += count($package['advisories']);
                }
            }

            $htaccess = [];
            if (isset($service['middleware'])) {
                $htaccess[] = $service['middleware'];
            }
            if (isset($service['details']['htaccess']['requireUser'])) {
                $htaccess[] = join(', ', $service['details']['htaccess']['requireUser']);
            }

            $data = [
                $service['env'],
                $service['server'],
                $service['stack'],
                $service['name'],
                join(', ', $service['urls']),
                join(', ', $htaccess),
                $service['repository']->image,
                '#' . substr((string) $service['repository']->imageDigest, -8),
                $service['details']['php']['version'] ?? '',
                $service['details']['symfony']['version'] ?? '',
                $security ?? '',
                isset($service['details']['php']['buildDate']) ? (new \DateTime($service['details']['php']['buildDate'] ?? ''))->format('d/m/Y H:i:s') : '',
                (new \DateTime($service['updatedAt']))->format('d/m/Y H:i:s') ?? '',
                $service['details']['git']['url'] ?? '',
                $service['details']['git']['branch'] ?? '',
            ];

            $rows[] = $data;
        }

        $fp = fopen('php://temp', 'w');

        foreach ($rows as $fields) {
            fputcsv($fp, $fields, separator: ';');
        }

        rewind($fp);
        $content = stream_get_contents($fp);
        fclose($fp);

        return $content;
    }
}
