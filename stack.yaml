version: '3.4'

services:
  front_web:
    image: gitlab.alienor.net:5050/dev-interne/tdb-docker-swarm:prod
    volumes:
      - /data/nas/containers/web-tdb-swarm/cfg/temp:/tmp
      - /data/nas/containers/web-tdb-swarm/cfg/swarmpit.yaml:/var/www/html/config/swarmpit.yaml
      - /data/nas/containers/web-tdb-swarm/cfg/data.db:/var/www/html/var/data.db
    env_file:
      - /data/nas/containers/web-tdb-swarm/cfg/.env.docker
    deploy:
      placement:
        constraints: [node.role == worker]

      labels:
        - traefik.enable=true
        - traefik.docker.network=traefik_public
        - traefik.constraint-label=traefik-public
        # Uses the environment variable DOMAIN
        - traefik.http.routers.web-tdb-swarm-public-http.rule=Host(`tdb-swarm.int.alienor.net`)
        - traefik.http.routers.web-tdb-swarm-public-http.entrypoints=http
        - traefik.http.routers.web-tdb-swarm-public-http.middlewares=https-redirect
        # traefik-https the actual router using HTTPS
        # Uses the environment variable DOMAIN
        - traefik.http.routers.web-tdb-swarm-public-https.rule=Host(`tdb-swarm.int.alienor.net`)
        - traefik.http.routers.web-tdb-swarm-public-https.entrypoints=https
        - traefik.http.routers.web-tdb-swarm-public-https.tls=true
        - traefik.http.services.web-tdb-swarm-public-https.loadbalancer.server.port=80

      mode: replicated
      replicas: 1
      update_config:
        delay: 10s
        order: start-first
      resources:
        limits:
          cpus: '4'
          memory: 512M
        reservations:
          cpus: '0.05'
          memory: 64M
    networks:
      - traefik
      - internal

  consumer:
    image: gitlab.alienor.net:5050/dev-interne/tdb-docker-swarm:prod
    command:
      ['sudo', '-HEu', 'www-data', 'php', '/var/www/html/bin/console', 'messenger:consume', 'async']
    volumes:
      - /data/nas/containers/web-tdb-swarm/cfg/temp:/tmp
      - /data/nas/containers/web-tdb-swarm/cfg/swarmpit.yaml:/var/www/html/config/swarmpit.yaml
      - /data/nas/containers/web-tdb-swarm/cfg/data.db:/var/www/html/var/data.db
    env_file:
      - /data/nas/containers/web-tdb-swarm/cfg/.env.consumer.docker
    healthcheck:
      test: ['CMD', 'echo', 'yes']
      interval: 10s
      timeout: 3s
      retries: 3
      start_period: 30s
    deploy:
      placement:
        constraints: [node.role == worker]
      mode: 'replicated'
      replicas: 1
      resources:
        limits:
          cpus: '8'
          memory: 512M
        reservations:
          cpus: '0.05'
          memory: 64M
      update_config:
        parallelism: 1
        delay: 10s
    networks:
      - internal

  scheduler:
    image: gitlab.alienor.net:5050/dev-interne/tdb-docker-swarm:prod
    command:
      [
        'sudo',
        '-HEu',
        'www-data',
        'php',
        '/var/www/html/bin/console',
        'messenger:consume',
        'scheduler_command'
      ]
    volumes:
      - /data/nas/containers/web-tdb-swarm/cfg/temp:/tmp
      - /data/nas/containers/web-tdb-swarm/cfg/swarmpit.yaml:/var/www/html/config/swarmpit.yaml
      - /data/nas/containers/web-tdb-swarm/cfg/data.db:/var/www/html/var/data.db
    env_file:
      - /data/nas/containers/web-tdb-swarm/cfg/.env.consumer.docker
    healthcheck:
      test: ['CMD', 'echo', 'yes']
      interval: 10s
      timeout: 3s
      retries: 3
      start_period: 30s
    deploy:
      placement:
        constraints: [node.role == worker]
      mode: 'replicated'
      replicas: 1
      resources:
        limits:
          cpus: '8'
          memory: 512M
        reservations:
          cpus: '0.05'
          memory: 64M
      update_config:
        parallelism: 1
        delay: 10s
    networks:
      - internal

  redis:
    image: redis:alpine
    volumes:
      - /data/nas/containers/web-tdb-swarm/redis:/data
    deploy:
      placement:
        constraints: [node.role == worker]
      mode: replicated
      replicas: 1
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.05'
          memory: 64M
    networks:
      - internal

  database:
    image: postgres:17.5-alpine
    volumes:
      - /data/nas/containers/web-tdb-swarm/cfg/database:/var/lib/postgresql/data
    environment:
      - POSTGRES_DB=app
      - POSTGRES_PASSWORD=DWbTHPYJL65l6K
      - POSTGRES_USER=app
    deploy:
      placement:
        constraints: [node.role == worker]
      mode: replicated
      replicas: 1
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.05'
          memory: 64M
    networks:
      - internal
networks:
  internal:
  traefik:
    external:
      name: traefik_public
