{"type": "project", "license": "proprietary", "minimum-stability": "beta", "prefer-stable": true, "require": {"php": ">=8.4", "ext-ctype": "*", "ext-iconv": "*", "doctrine/annotations": "^1.0", "doctrine/dbal": "^3", "doctrine/doctrine-bundle": "^2.13", "doctrine/doctrine-migrations-bundle": "^3.4", "doctrine/orm": "^2.20", "dragonmantank/cron-expression": "^3.4", "pentatrion/vite-bundle": "^8.1", "phpdocumentor/reflection-docblock": "^5.3", "phpstan/phpdoc-parser": "^1.8", "runtime/frankenphp-symfony": "^0.2.0", "spatie/fork": "^1.2", "symfony/apache-pack": "^1.0", "symfony/console": "7.3.*", "symfony/css-selector": "7.3.*", "symfony/dom-crawler": "7.3.*", "symfony/dotenv": "7.3.*", "symfony/flex": "^2", "symfony/form": "7.3.*", "symfony/framework-bundle": "7.3.*", "symfony/http-client": "7.3.*", "symfony/lock": "7.3.*", "symfony/maker-bundle": "^1.45", "symfony/mercure-bundle": "^0.3.5", "symfony/messenger": "7.3.*", "symfony/monolog-bundle": "^3.0", "symfony/property-access": "7.3.*", "symfony/property-info": "7.3.*", "symfony/redis-messenger": "^7.2", "symfony/runtime": "7.3.*", "symfony/scheduler": "7.3.*", "symfony/serializer": "7.3.*", "symfony/translation": "7.3.*", "symfony/twig-bundle": "7.3.*", "symfony/validator": "7.3.*", "symfony/yaml": "7.3.*", "twig/extra-bundle": "^3.21", "twig/twig": "^3.21", "zenstruck/messenger-monitor-bundle": "^0.5.1"}, "config": {"allow-plugins": {"composer/package-versions-deprecated": true, "symfony/flex": true, "symfony/runtime": true}, "optimize-autoloader": true, "preferred-install": {"*": "dist"}, "sort-packages": true}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*", "symfony/polyfill-php73": "*", "symfony/polyfill-php74": "*", "symfony/polyfill-php80": "*", "symfony/polyfill-php81": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "7.3.*"}}, "require-dev": {"clue/phar-composer": "1.x-dev", "friendsofphp/php-cs-fixer": "^3.11", "rector/rector": "^2.0", "symfony/debug-bundle": "7.3.*", "symfony/stopwatch": "7.3.*", "symfony/web-profiler-bundle": "7.3.*"}, "repositories": [{"type": "git", "url": "https://github.com/ChqThomas/phar-composer"}]}