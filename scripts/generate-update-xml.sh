#!/bin/bash

# Script pour générer le fichier XML de mise à jour
set -e

# Variables
EXTENSION_DIR="chrome-extension"
DIST_DIR="dist"
VERSION=$(grep '"version"' $EXTENSION_DIR/manifest.json | sed 's/.*"version": "\([^"]*\)".*/\1/')
PROJECT_ID="591"
GITLAB_URL="https://gitlab.alienor.net"
EXTENSION_ID="jibjbdlllaiiilecjdlekpjmhoogkjoe"

if [ -z "$EXTENSION_ID" ]; then
    echo "Warning: CHROME_EXTENSION_ID not set. You'll need to update this after first installation."
    EXTENSION_ID="YOUR_EXTENSION_ID_HERE"
fi

echo "Generating update XML for version $VERSION..."

# Créer le répertoire de distribution
mkdir -p $DIST_DIR

# Générer le fichier updates.xml
cat > $DIST_DIR/updates.xml << EOF
<?xml version='1.0' encoding='UTF-8'?>
<gupdate xmlns='http://www.google.com/update2/response' protocol='2.0'>
  <app appid='$EXTENSION_ID'>
    <updatecheck codebase='$GITLAB_URL/api/v4/projects/$PROJECT_ID/packages/generic/chrome-extension/$VERSION/alienor-webhost-extension-$VERSION.crx' version='$VERSION' />
  </app>
</gupdate>
EOF

echo "Update XML generated: $DIST_DIR/updates.xml"
echo "Extension ID: $EXTENSION_ID"
echo "Version: $VERSION"
echo "Download URL: $GITLAB_URL/api/v4/projects/$PROJECT_ID/packages/generic/chrome-extension/$VERSION/alienor-webhost-extension-$VERSION.crx"
