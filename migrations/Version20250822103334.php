<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250822103334 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE processed_messages (id SERIAL NOT NULL, run_id INT NOT NULL, attempt SMALLINT NOT NULL, message_type VARCHAR(255) NOT NULL, description VARCHAR(255) DEFAULT NULL, dispatched_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, received_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, finished_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, wait_time BIGINT NOT NULL, handle_time BIGINT NOT NULL, memory_usage INT NOT NULL, transport VARCHAR(255) NOT NULL, tags VARCHAR(255) DEFAULT NULL, failure_type VARCHAR(255) DEFAULT NULL, failure_message TEXT DEFAULT NULL, results JSON DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('COMMENT ON COLUMN processed_messages.dispatched_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN processed_messages.received_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN processed_messages.finished_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE web_host (id SERIAL NOT NULL, configuration_id INT DEFAULT NULL, name VARCHAR(255) DEFAULT NULL, environnement VARCHAR(255) DEFAULT NULL, expected_visibility VARCHAR(255) DEFAULT NULL, gitlab_remote_url VARCHAR(255) DEFAULT NULL, gitlab_active_branch VARCHAR(255) DEFAULT NULL, last_commit_date VARCHAR(255) DEFAULT NULL, confluence_url VARCHAR(255) DEFAULT NULL, database_explorer_url VARCHAR(255) DEFAULT NULL, associated_urls JSON DEFAULT \'[]\' NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, updated_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_B251E0873F32DD8 ON web_host (configuration_id)');
        $this->addSql('CREATE TABLE web_host_configuration (id SERIAL NOT NULL, hostType VARCHAR(255) NOT NULL, ip VARCHAR(255) DEFAULT NULL, location VARCHAR(255) DEFAULT NULL, cluster_name VARCHAR(255) DEFAULT NULL, swarmpit_url VARCHAR(255) DEFAULT NULL, stack VARCHAR(255) DEFAULT NULL, service_name VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE TABLE web_host_url (id SERIAL NOT NULL, web_host_id INT NOT NULL, url VARCHAR(255) DEFAULT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, updated_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, health_report_status VARCHAR(255) DEFAULT NULL, health_report_url VARCHAR(255) DEFAULT NULL, health_report_status_code INT DEFAULT NULL, ssl_report_statuses JSON DEFAULT NULL, ssl_report_is_valid BOOLEAN DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_1530F3AEF7177F1B ON web_host_url (web_host_id)');
        $this->addSql('ALTER TABLE web_host ADD CONSTRAINT FK_B251E0873F32DD8 FOREIGN KEY (configuration_id) REFERENCES web_host_configuration (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE web_host_url ADD CONSTRAINT FK_1530F3AEF7177F1B FOREIGN KEY (web_host_id) REFERENCES web_host (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE web_host DROP CONSTRAINT FK_B251E0873F32DD8');
        $this->addSql('ALTER TABLE web_host_url DROP CONSTRAINT FK_1530F3AEF7177F1B');
        $this->addSql('DROP TABLE processed_messages');
        $this->addSql('DROP TABLE web_host');
        $this->addSql('DROP TABLE web_host_configuration');
        $this->addSql('DROP TABLE web_host_url');
    }
}
