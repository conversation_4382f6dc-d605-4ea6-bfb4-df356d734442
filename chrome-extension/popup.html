<!doctype html>
<html>
	<head>
		<meta charset="utf-8" />
		<title>WebHost Links</title>
		<style>
			* {
				box-sizing: border-box;
			}

			body {
				width: 380px;
				margin: 0;
				padding: 0;
				font-family:
					-apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
				font-size: 13px;
				line-height: 1.4;
				background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
				color: #333;
			}

			.container {
				background: white;
				border-radius: 12px;
				margin: 8px;
				box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
				overflow: hidden;
			}

			.header {
				background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
				color: white;
				padding: 16px;
				text-align: center;
			}

			.header h1 {
				margin: 0 0 8px 0;
				font-size: 18px;
				font-weight: 600;
				text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
			}

			.current-url {
				font-size: 11px;
				opacity: 0.9;
				word-break: break-all;
				background: rgba(255, 255, 255, 0.1);
				padding: 6px 10px;
				border-radius: 20px;
				margin-top: 8px;
			}

			.content {
				padding: 16px;
			}

			.loading {
				text-align: center;
				padding: 32px 16px;
				color: #666;
			}

			.loading::before {
				content: '⏳';
				display: block;
				font-size: 24px;
				margin-bottom: 8px;
			}

			.error {
				background: linear-gradient(135deg, #ff6b6b, #ee5a52);
				color: white;
				border-radius: 8px;
				padding: 12px;
				margin-bottom: 16px;
				text-align: center;
			}

			.error::before {
				content: '⚠️';
				display: block;
				font-size: 20px;
				margin-bottom: 4px;
			}

			.webhost-card {
				background: linear-gradient(135deg, #f8f9fa, #e9ecef);
				border-radius: 10px;
				padding: 16px;
				margin-bottom: 16px;
				border: 1px solid #dee2e6;
			}

			.webhost-header {
				display: flex;
				align-items: center;
				margin-bottom: 8px;
			}

			.webhost-icon {
				width: 32px;
				height: 32px;
				background: linear-gradient(135deg, #667eea, #764ba2);
				border-radius: 8px;
				display: flex;
				align-items: center;
				justify-content: center;
				color: white;
				font-weight: bold;
				margin-right: 12px;
				font-size: 14px;
			}

			.webhost-info {
				flex: 1;
			}

			.webhost-name {
				font-weight: 600;
				color: #333;
				margin: 0 0 2px 0;
				font-size: 15px;
			}

			.webhost-meta {
				font-size: 11px;
				color: #666;
				display: flex;
				gap: 8px;
				flex-wrap: wrap;
			}

			.meta-badge {
				background: #e9ecef;
				padding: 2px 6px;
				border-radius: 10px;
				text-transform: uppercase;
				font-weight: 500;
			}

			.links-grid {
				display: grid;
				gap: 4px;
				margin-bottom: 16px;
			}

			.link-card {
				background: white;
				border: 1px solid #e9ecef;
				border-radius: 6px;
				padding: 8px 10px;
				transition: all 0.2s ease;
				cursor: pointer;
				display: flex;
				align-items: center;
				justify-content: space-between;
			}

			.link-card:hover {
				border-color: #667eea;
				box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
				transform: translateY(-1px);
			}

			.link-main {
				display: flex;
				align-items: center;
				flex: 1;
				min-width: 0;
			}

			.link-icon {
				font-size: 14px;
				margin-right: 8px;
				width: 16px;
				text-align: center;
				flex-shrink: 0;
			}

			.link-info {
				flex: 1;
				min-width: 0;
			}

			.link-title {
				font-weight: 500;
				color: #333;
				font-size: 12px;
				margin-bottom: 1px;
			}

			.link-url {
				color: #667eea;
				text-decoration: none;
				font-size: 10px;
				word-break: break-all;
				display: block;
				opacity: 0.8;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			}

			.link-url:hover {
				text-decoration: underline;
			}

			.status-indicator {
				width: 6px;
				height: 6px;
				border-radius: 50%;
				flex-shrink: 0;
				margin-left: 8px;
			}

			.status-ok {
				background-color: #28a745;
				box-shadow: 0 0 4px rgba(40, 167, 69, 0.4);
			}

			.status-error {
				background-color: #dc3545;
				box-shadow: 0 0 4px rgba(220, 53, 69, 0.4);
			}

			.status-unknown {
				background-color: #6c757d;
			}

			.no-match {
				text-align: center;
				padding: 32px 16px;
				color: #666;
			}

			.no-match::before {
				content: '🔍';
				display: block;
				font-size: 32px;
				margin-bottom: 12px;
				opacity: 0.5;
			}

			.cache-info {
				font-size: 10px;
				color: #999;
				text-align: center;
				padding: 8px;
				background: #f8f9fa;
				border-top: 1px solid #e9ecef;
				margin: 0 -16px -16px -16px;
			}

			.clear-cache-link {
				color: #667eea;
				text-decoration: none;
				font-weight: 500;
			}

			.clear-cache-link:hover {
				text-decoration: underline;
			}

			.section-title {
				font-size: 12px;
				font-weight: 600;
				color: #666;
				text-transform: uppercase;
				letter-spacing: 0.5px;
				margin: 16px 0 8px 0;
				padding-bottom: 4px;
				border-bottom: 1px solid #e9ecef;
			}

			.section-title:first-child {
				margin-top: 0;
			}

			.git-conversion-indicator {
				color: #28a745;
				font-size: 10px;
				font-weight: 500;
				margin-left: 4px;
			}

			.hosting-info {
				background: #f8f9fa;
				border: 1px solid #e9ecef;
				border-radius: 8px;
				padding: 12px;
				margin-bottom: 16px;
			}

			.hosting-title {
				font-size: 12px;
				font-weight: 600;
				color: #495057;
				margin-bottom: 8px;
				text-transform: uppercase;
				letter-spacing: 0.5px;
			}

			.hosting-details {
				display: grid;
				gap: 6px;
			}

			.hosting-item {
				display: flex;
				align-items: center;
				font-size: 11px;
			}

			.hosting-label {
				font-weight: 500;
				color: #6c757d;
				min-width: 60px;
				margin-right: 8px;
			}

			.hosting-value {
				color: #495057;
				font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
				background: #e9ecef;
				padding: 2px 6px;
				border-radius: 4px;
				font-size: 10px;
			}

			.tech-info {
				background: #f8f9fa;
				border: 1px solid #e9ecef;
				border-radius: 8px;
				padding: 12px;
				margin-bottom: 16px;
			}

			.tech-title {
				font-size: 12px;
				font-weight: 600;
				color: #495057;
				margin-bottom: 8px;
				text-transform: uppercase;
				letter-spacing: 0.5px;
			}

			.tech-details {
				display: grid;
				gap: 6px;
			}

			.tech-item {
				display: flex;
				align-items: center;
				font-size: 11px;
			}

			.tech-label {
				font-weight: 500;
				color: #6c757d;
				min-width: 80px;
				margin-right: 8px;
			}

			.tech-value {
				color: #495057;
				font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
				background: #e9ecef;
				padding: 2px 6px;
				border-radius: 4px;
				font-size: 10px;
			}

			.tech-status {
				margin-left: 6px;
				font-size: 9px;
				padding: 1px 4px;
				border-radius: 3px;
				font-weight: 500;
			}

			.tech-status.outdated {
				background: #fff3cd;
				color: #856404;
			}

			.tech-status.eol {
				background: #f8d7da;
				color: #721c24;
			}

			.tech-status.current {
				background: #d4edda;
				color: #155724;
			}
		</style>
	</head>
	<body>
		<div class="container">
			<div class="header">
				<h1>WebHost Links</h1>
				<div class="current-url" id="currentUrl">Chargement...</div>
			</div>

			<div class="content">
				<div id="content">
					<div class="loading">Recherche des liens...</div>
				</div>
			</div>
		</div>

		<script src="config.js"></script>
		<script src="popup.js"></script>
	</body>
</html>
