// Version refactorisée du popup avec séparation des responsabilités
import { WebHostService, LinkService } from './services/webhost-service.js';
import { templates } from './utils/templates.js';

class PopupApp {
  constructor() {
    this.webHostService = new WebHostService();
    this.linkService = new LinkService();
    this.contentElement = null;
  }

  async init() {
    this.contentElement = document.getElementById('content');
    
    try {
      // Obtenir l'URL de l'onglet actif
      const currentUrl = await this.getCurrentTabUrl();
      debugLog("URL de l'onglet actif:", currentUrl);
      document.getElementById('currentUrl').textContent = currentUrl;

      // Récupérer les données des webhosts
      const webHosts = await this.webHostService.getWebHosts();
      debugLog('Nombre de webhosts récupérés:', webHosts.length);

      // Trouver le webhost correspondant
      const matchingWebHost = this.webHostService.findMatchingWebHost(currentUrl, webHosts);

      if (matchingWebHost) {
        debugLog('Webhost correspondant trouvé:', matchingWebHost.name);
        this.displayWebHostInfo(matchingWebHost);
      } else {
        debugLog('Aucun webhost correspondant trouvé');
        this.displayNoMatch();
      }
    } catch (error) {
      debugLog('Erreur dans init():', error);
      this.displayError(error.message);
    }
  }

  async getCurrentTabUrl() {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    return tab.url;
  }

  displayWebHostInfo(webHost) {
    const content = [];

    // Carte du webhost
    content.push(templates.webHostCard(webHost));

    // Bloc hébergement
    if (webHost.configuration) {
      content.push(templates.hostingInfo(webHost.configuration));
    }

    // Bloc informations techniques
    if (webHost.service) {
      const techInfo = templates.techInfo(webHost.service.details, webHost.service.repository);
      if (techInfo) {
        content.push(techInfo);
      }
    }

    // Grille des liens
    const links = this.linkService.extractLinksFromWebHost(webHost);
    if (links.length > 0) {
      const linksGrid = `
        <div class="links-grid">
          ${links.map(link => templates.linkCard(link)).join('')}
        </div>
      `;
      content.push(linksGrid);
    }

    // Info cache en mode debug
    if (CONFIG.DEBUG) {
      content.push(`
        <div class="cache-info">
          💾 Données mises en cache (1h) • 
          <a href="#" class="clear-cache-link" id="clearCache">Vider le cache</a>
        </div>
      `);
    }

    this.contentElement.innerHTML = content.join('');
    this.attachEventListeners();
  }

  displayError(message) {
    this.contentElement.innerHTML = templates.error(message);
  }

  displayNoMatch() {
    this.contentElement.innerHTML = templates.noMatch(CONFIG.DEBUG);
    
    if (CONFIG.DEBUG) {
      this.attachClearCacheListener();
    }
  }

  attachEventListeners() {
    // Gestionnaire pour les cartes de liens
    const linkCards = this.contentElement.querySelectorAll('.link-card');
    linkCards.forEach(card => {
      card.addEventListener('click', (e) => {
        if (e.target.tagName !== 'A') {
          const url = card.dataset.url;
          if (url) {
            window.open(url, '_blank');
          }
        }
      });
    });

    // Gestionnaire pour vider le cache (mode debug)
    if (CONFIG.DEBUG) {
      this.attachClearCacheListener();
    }
  }

  attachClearCacheListener() {
    const clearCacheLink = document.getElementById('clearCache');
    if (clearCacheLink) {
      clearCacheLink.addEventListener('click', async (e) => {
        e.preventDefault();
        try {
          await this.webHostService.clearCache();
          debugLog('Cache vidé, rechargement...');
          window.location.reload();
        } catch (error) {
          debugLog('Erreur lors du vidage du cache:', error);
        }
      });
    }
  }
}

// Initialisation de l'application
document.addEventListener('DOMContentLoaded', () => {
  const app = new PopupApp();
  app.init();
});
