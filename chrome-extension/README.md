# WebHost Links Extension

Cette extension Chrome permet d'afficher les liens utiles (GitLab, Confluence, Base de données, etc.) pour l'URL de l'onglet actif en se connectant à l'API `/webHosts/data`.

## Fonctionnalités

- **Détection automatique** : L'URL de l'onglet actif est analysée automatiquement
- **Badge intelligent** : Affiche un badge vert (✓) sur l'icône quand un webhost est trouvé
- **Cache optimisé** : Les données API sont mises en cache pendant 1 heure pour de meilleures performances
- **Connexion API** : Se connecte à l'API `/webHosts/data` pour récupérer les informations des webhosts
- **Correspondance intelligente** : Trouve le webhost correspondant en comparant les domaines
- **Interface complète** : Affiche dans une popup tous les liens utiles :
  - GitLab (repository) avec icône 🦊
  - Confluence (documentation) avec icône 📖
  - Base de données (adminer/explorer) avec icône 🗄️
  - URLs du service avec indicateurs de statut de santé (vert/rouge/gris)
  - URLs associées avec icône 🔗
  - Informations Git (branche active et date du dernier commit)

## Configuration

Avant d'utiliser l'extension, vous devez configurer l'URL de base de votre API :

1. Copiez le fichier `config.example.js` vers `config.js`
2. Modifiez les valeurs dans `config.js` selon votre environnement :

```javascript
const CONFIG = {
	// URL de base de votre API (sans le slash final)
	API_BASE_URL: 'http://localhost', // Remplacez par votre URL

	// Endpoint pour récupérer les données des webhosts
	API_ENDPOINT: '/api/webHosts/data',

	// Timeout pour les requêtes API (en millisecondes)
	API_TIMEOUT: 10000,

	// Activer les logs de debug dans la console
	DEBUG: true
};
```

## Installation

1. Clonez ce repository
2. Ouvrez Chrome et allez dans `chrome://extensions/`
3. Activez le "Mode développeur" en haut à droite
4. Cliquez sur "Charger l'extension non empaquetée"
5. Sélectionnez le dossier `chrome-extension`
6. L'extension apparaît dans la barre d'outils de Chrome

## Utilisation

1. **Navigation** : Naviguez vers une URL qui correspond à un webhost dans votre système
2. **Badge automatique** : Si un webhost est trouvé, un badge vert (✓) apparaît sur l'icône de l'extension
3. **Popup** : Cliquez sur l'icône de l'extension pour ouvrir la popup avec tous les liens
4. **Liens** : Cliquez sur les liens pour les ouvrir dans de nouveaux onglets
5. **Cache** : Les données sont automatiquement mises en cache pendant 1 heure pour de meilleures performances

### Indicateurs visuels

- **Badge vert (✓)** : Un webhost correspondant a été trouvé pour cette URL
- **Pas de badge** : Aucun webhost trouvé pour cette URL
- **Statut de santé** : Points colorés à côté des URLs de service (vert=OK, rouge=erreur, gris=inconnu)

## Structure des données

L'extension s'attend à recevoir des données au format suivant depuis l'API `/webHosts/data` :

```json
{
	"webHosts": [
		{
			"id": 3,
			"name": "Time",
			"environnement": "dev",
			"expectedVisibility": "external",
			"gitlabRemoteUrl": "https://gitlab.example.com/project",
			"confluenceUrl": "https://confluence.example.com/page",
			"databaseUrl": "https://adminer.example.com",
			"urls": [
				{
					"id": 3,
					"url": "http://time.int.example.com",
					"healthCheckReport": {
						"status": "OK"
					}
				}
			],
			"associatedUrls": [
				{
					"url": "https://docs.example.com"
				}
			]
		}
	]
}
```

## Permissions

L'extension demande les permissions suivantes :

- `activeTab` : pour accéder à l'URL de l'onglet actif
- `tabs` : pour détecter les changements d'onglet et mettre à jour les badges
- `storage` : pour le cache des données API (1 heure)
- `host_permissions` : pour accéder à l'API locale

## Cache et performances

- **Cache automatique** : Les données de l'API sont automatiquement mises en cache pendant 1 heure
- **Badges en temps réel** : Les badges sont mis à jour automatiquement lors des changements d'onglet
- **Optimisation réseau** : Évite les appels API répétés grâce au système de cache
- **Mode debug** : En mode debug, vous pouvez vider le cache manuellement depuis la popup

## Développement

### Prérequis

- **Node.js** et **npm**
- **crx3** pour le packaging : `npm install -g crx3`

### Modification de l'extension

Pour modifier l'extension :

1. Éditez les fichiers dans le dossier `chrome-extension`
2. Rechargez l'extension dans `chrome://extensions/`
3. Testez les modifications

### Scripts disponibles

```bash
# Construire l'extension en CRX et générer le XML de mise à jour
task extension-build
```

```bash
# Déployer l'extension (upload CRX et XML)
task extension-deploy
```

### Packaging avec crx3

L'extension utilise `crx3` pour créer les fichiers CRX pour la distribution :

```bash
# Installation de crx3
npm install -g crx3

# Packaging manuel
crx3 --crx "./dist/extension.crx" --key ./scripts/chrome-extension.pem ./chrome-extension
```

### Structure des fichiers de build

- `manifest.json` : Configuration de l'extension
- `popup.html` : Interface utilisateur de la popup
- `popup.js` : Logique de la popup
- `service-worker.js` : Service worker pour les tâches en arrière-plan
- `config.js` : Configuration des URLs et paramètres
- `icon.png` : Icône de l'extension

## Déploiement automatique

L'extension est automatiquement packagée et publiée dans GitLab Package Registry via GitLab CI.

Voir le fichier [DEPLOYMENT.md](DEPLOYMENT.md) pour les instructions complètes de déploiement automatique.
