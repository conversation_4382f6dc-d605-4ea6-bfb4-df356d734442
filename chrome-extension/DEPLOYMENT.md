# Déploiement de l'Extension Chrome

## Configuration GitLab

### Variables d'environnement à configurer

Dans GitLab, allez dans **Settings > CI/CD > Variables** et ajoutez :

1. **CHROME_EXTENSION_ID** (optionnel au début)
   - Valeur : L'ID de votre extension Chrome (sera généré après la première installation)
   - Type : Variable
   - Environnement : Tous
   - Protégé : Non
   - Masqué : Non

### Obtenir l'ID de l'extension

1. Installez l'extension manuellement la première fois
2. Allez dans `chrome://extensions/`
3. Activez le "Mode développeur"
4. L'ID de l'extension apparaîtra sous le nom de l'extension
5. Copiez cet ID et ajoutez-le comme variable d'environnement `CHROME_EXTENSION_ID`

### Mise à jour du manifest

Remplacez `YOUR_PROJECT_ID` dans le manifest.json par l'ID réel de votre projet GitLab :

```json
"update_url": "https://gitlab.alienor.net/api/v4/projects/VOTRE_PROJECT_ID/packages/generic/chrome-extension/latest/updates.xml"
```

## Processus de déploiement

### Déclenchement automatique

Le pipeline se déclenche automatiquement quand :

- Des modifications sont apportées au dossier `chrome-extension/`
- Sur les branches `master` ou `develop`

### Prérequis

- **crx3** : Outil de packaging pour extensions Chrome
  ```bash
  npm install -g crx3
  ```

### Étapes du pipeline

1. **Build de l'extension** : Création du fichier CRX avec crx3
2. **Génération du XML de mise à jour** : Fichier nécessaire pour les mises à jour automatiques
3. **Publication dans GitLab Package Registry** : Upload des fichiers

### Fichiers générés

- `alienor-webhost-extension-{version}.crx` : Extension packagée
- `updates.xml` : Fichier de mise à jour automatique

## URLs d'accès

### Téléchargement direct

```
https://gitlab.alienor.net/api/v4/projects/{PROJECT_ID}/packages/generic/chrome-extension/{VERSION}/alienor-webhost-extension-{VERSION}.zip
```

### Mise à jour automatique

```
https://gitlab.alienor.net/api/v4/projects/{PROJECT_ID}/packages/generic/chrome-extension/latest/updates.xml
```

## Installation pour les utilisateurs

### Première installation

1. Télécharger le fichier ZIP depuis GitLab Package Registry
2. Extraire dans un dossier local
3. Ouvrir `chrome://extensions/`
4. Activer le "Mode développeur"
5. Cliquer "Charger l'extension non empaquetée"
6. Sélectionner le dossier extrait

### Mises à jour automatiques

Une fois installée avec l'`update_url` configurée, Chrome vérifiera automatiquement les mises à jour et les installera.

## Gestion des versions

### Incrémenter la version

1. Modifier le champ `version` dans `chrome-extension/manifest.json`
2. Commiter et pousser sur `master` ou `develop`
3. Le pipeline se déclenchera automatiquement

### Format de version

Utilisez le format semver : `MAJOR.MINOR.PATCH`

- `1.0.0` : Version initiale
- `1.0.1` : Correction de bug
- `1.1.0` : Nouvelle fonctionnalité
- `2.0.0` : Changement majeur

## Dépannage

### Extension non mise à jour

1. Vérifiez que `CHROME_EXTENSION_ID` est correctement configuré
2. Vérifiez que l'`update_url` dans le manifest pointe vers le bon projet
3. Forcez la vérification dans `chrome://extensions/` > "Mettre à jour"

### Erreur de packaging

1. Vérifiez que `crx3` est installé : `npm install -g crx3`
2. Vérifiez que tous les fichiers nécessaires sont présents
3. Vérifiez la syntaxe du manifest.json
4. Vérifiez que la clé privée est générée correctement
5. Consultez les logs du pipeline GitLab

### Problème d'accès aux packages

1. Vérifiez les permissions du projet GitLab
2. Assurez-vous que le Package Registry est activé
3. Vérifiez les tokens d'accès si nécessaire
