// Service pour gérer les données des webhosts
export class WebHostService {
  constructor() {
    this.cache = new Map();
    this.cacheTimeout = 60 * 60 * 1000; // 1 heure
  }

  async getWebHosts() {
    try {
      debugLog('Récupération des webhosts depuis le cache du service worker');

      // Demander les données au service worker (qui gère le cache)
      const response = await chrome.runtime.sendMessage({ action: 'getCachedWebHosts' });

      if (response && response.webHosts) {
        debugLog('Données reçues du service worker:', response.webHosts.length, 'webhosts');
        return response.webHosts;
      }

      // Fallback : récupération directe si le service worker ne répond pas
      debugLog("Fallback: récupération directe depuis l'API");
      return await this.fetchWebHostsDirect();
    } catch (error) {
      debugLog('Erreur lors de la récupération depuis le service worker, tentative directe:', error);
      return await this.fetchWebHostsDirect();
    }
  }

  async fetchWebHostsDirect() {
    try {
      debugLog('Récupération directe depuis:', `${CONFIG.API_BASE_URL}${CONFIG.API_ENDPOINT}`);

      const response = await fetch(`${CONFIG.API_BASE_URL}${CONFIG.API_ENDPOINT}`, {
        method: 'GET',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json'
        },
        timeout: CONFIG.API_TIMEOUT
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      debugLog('Données reçues directement:', data);

      return data.webHosts || [];
    } catch (error) {
      debugLog('Erreur lors de la récupération directe des webhosts:', error);

      // Gestion spécifique des erreurs CORS
      if (error.message.includes('CORS') || error.message.includes('fetch')) {
        throw new Error(
          `Impossible de se connecter à l'API (${CONFIG.API_BASE_URL}). Vérifiez que l'API est accessible et que les permissions CORS sont configurées.`
        );
      }

      throw error;
    }
  }

  findMatchingWebHost(currentUrl, webHosts) {
    const currentDomain = this.extractDomain(currentUrl);
    debugLog('Recherche du webhost pour le domaine:', currentDomain);

    for (const webHost of webHosts) {
      debugLog('Vérification du webhost:', webHost.name);

      // Vérifier les URLs principales
      if (webHost.urls && webHost.urls.length > 0) {
        for (const urlObj of webHost.urls) {
          const webhostDomain = this.extractDomain(urlObj.url);
          debugLog('  - URL principale:', urlObj.url, '-> domaine:', webhostDomain);
          if (webhostDomain === currentDomain) {
            debugLog('✓ Correspondance trouvée avec URL principale');
            return webHost;
          }
        }
      }

      // Vérifier aussi les URLs associées
      if (webHost.associatedUrls && webHost.associatedUrls.length > 0) {
        for (const associatedUrl of webHost.associatedUrls) {
          const associatedDomain = this.extractDomain(associatedUrl.url);
          debugLog('  - URL associée:', associatedUrl.url, '-> domaine:', associatedDomain);
          if (associatedDomain === currentDomain) {
            debugLog('✓ Correspondance trouvée avec URL associée');
            return webHost;
          }
        }
      }
    }

    debugLog('✗ Aucune correspondance trouvée');
    return null;
  }

  extractDomain(url) {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname;
    } catch (error) {
      debugLog('URL invalide:', url, error);
      return '';
    }
  }

  async clearCache() {
    try {
      await chrome.runtime.sendMessage({ action: 'clearCache' });
      debugLog('Cache vidé');
    } catch (error) {
      debugLog('Erreur lors du vidage du cache:', error);
      throw error;
    }
  }
}

// Service pour gérer les liens
export class LinkService {
  constructor() {
    this.gitUrlConverter = new GitUrlConverter();
  }

  extractLinksFromWebHost(webHost) {
    const links = [];

    // GitLab
    if (webHost.gitlabRemoteUrl) {
      const gitlabUrl = this.gitUrlConverter.convertToHttp(webHost.gitlabRemoteUrl);
      links.push({
        type: 'gitlab',
        title: 'GitLab',
        url: gitlabUrl,
        originalUrl: webHost.gitlabRemoteUrl
      });
    }

    // Confluence
    if (webHost.confluenceUrl) {
      links.push({ 
        type: 'confluence', 
        title: 'Confluence', 
        url: webHost.confluenceUrl 
      });
    }

    // Base de données
    const databaseUrl = webHost.databaseUrl || webHost.databaseExplorerUrl;
    if (databaseUrl) {
      links.push({ 
        type: 'database', 
        title: 'Base de données', 
        url: databaseUrl 
      });
    }

    // URLs associées
    if (webHost.associatedUrls && webHost.associatedUrls.length > 0) {
      webHost.associatedUrls.forEach((associatedUrl) => {
        links.push({
          type: 'associated',
          title: 'Lien associé',
          url: associatedUrl.url
        });
      });
    }

    // Docker Tools et services
    if (webHost.service) {
      // TDB Swarm
      links.push({
        type: 'docker',
        title: 'TDB Swarm',
        label: 'Ouvrir dans TDB Swarm',
        url: 'https://tdb-swarm.int.alienor.net#' + webHost.service.id
      });

      // Logs Docker
      if (webHost.service.links?.logs) {
        links.push({
          type: 'docker',
          title: 'Logs Docker',
          label: 'Ouvrir dans Swarmpit',
          url: webHost.service.links.logs
        });
      }

      // Docker Tools Command depuis les tâches
      if (webHost.service.tasks && webHost.service.tasks.length > 0) {
        const firstTask = webHost.service.tasks[0];
        if (firstTask.dockerToolsCommand) {
          links.push({
            type: 'docker',
            title: 'Docker Tools',
            url: firstTask.dockerToolsCommand
          });
        }
      }
    }

    return links;
  }
}

// Utilitaire pour convertir les URLs Git
class GitUrlConverter {
  convertToHttp(gitUrl) {
    if (!gitUrl) return gitUrl;

    // Si c'est déjà une URL HTTP/HTTPS, la retourner telle quelle
    if (gitUrl.startsWith('http://') || gitUrl.startsWith('https://')) {
      return gitUrl;
    }

    // Si ce n'est pas une URL Git SSH, la retourner telle quelle
    if (!gitUrl.startsWith('git@')) {
      return gitUrl;
    }

    try {
      debugLog('Conversion URL Git SSH:', gitUrl);

      // Extraire la partie après git@
      const withoutPrefix = gitUrl.substring(4); // Enlever "git@"

      // Séparer hostname et path
      const colonIndex = withoutPrefix.indexOf(':');
      if (colonIndex === -1) {
        debugLog('Format Git SSH invalide (pas de :):', gitUrl);
        return gitUrl;
      }

      const hostname = withoutPrefix.substring(0, colonIndex);
      let path = withoutPrefix.substring(colonIndex + 1);

      // Gérer le cas où il y a un port (ex: git@hostname:22/path)
      if (path.match(/^\d+\//)) {
        const slashIndex = path.indexOf('/');
        if (slashIndex !== -1) {
          path = path.substring(slashIndex + 1);
        }
      }

      // Enlever l'extension .git si présente
      if (path.endsWith('.git')) {
        path = path.substring(0, path.length - 4);
      }

      // Construire l'URL HTTP
      const httpUrl = `https://${hostname}/${path}`;

      debugLog('URL Git convertie:', gitUrl, '->', httpUrl);
      return httpUrl;
    } catch (error) {
      debugLog("Erreur lors de la conversion de l'URL Git:", error);
      return gitUrl; // En cas d'erreur, retourner l'URL originale
    }
  }
}
