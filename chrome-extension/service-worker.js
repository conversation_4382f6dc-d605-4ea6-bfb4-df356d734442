// Service worker pour l'extension WebHost Links
// Gère les badges et le cache des données

// Configuration du cache
const CACHE_KEY = 'webhost_data';
const CACHE_DURATION = 60 * 60 * 1000; // 1 heure en millisecondes

// Configuration par défaut (sera écrasée par config.js dans la popup)
const DEFAULT_CONFIG = {
	API_BASE_URL: 'https://tdb-swarm.int.alienor.net/',
	API_ENDPOINT: '/api/chrome-extension/data',
	API_TIMEOUT: 10000
};

// Installation de l'extension
chrome.runtime.onInstalled.addListener(() => {
	console.log('Extension WebHost Links installée');
	// Nettoyer le cache au démarrage
	chrome.storage.local.remove([CACHE_KEY]);
});

// Écouter les changements d'onglet pour mettre à jour le badge
chrome.tabs.onActivated.addListener(async (activeInfo) => {
	await updateBadgeForTab(activeInfo.tabId);
});

// Écouter les mises à jour d'URL dans les onglets
chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
	if (changeInfo.status === 'complete' && tab.url) {
		await updateBadgeForTab(tabId);
	}
});

// Fonction pour récupérer les données depuis le cache ou l'API
async function getCachedWebHosts() {
	try {
		// Vérifier le cache
		const cached = await chrome.storage.local.get([CACHE_KEY]);
		const now = Date.now();

		if (
			cached[CACHE_KEY] &&
			cached[CACHE_KEY].timestamp &&
			now - cached[CACHE_KEY].timestamp < CACHE_DURATION
		) {
			console.log('[WebHost Links] Utilisation du cache');
			return cached[CACHE_KEY].data;
		}

		// Cache expiré ou inexistant, récupérer depuis l'API
		console.log("[WebHost Links] Récupération depuis l'API");
		const response = await fetch(`${DEFAULT_CONFIG.API_BASE_URL}${DEFAULT_CONFIG.API_ENDPOINT}`, {
			method: 'GET',
			headers: {
				Accept: 'application/json',
				'Content-Type': 'application/json'
			}
		});

		if (!response.ok) {
			throw new Error(`HTTP ${response.status}: ${response.statusText}`);
		}

		const data = await response.json();
		const webHosts = data.webHosts || [];

		// Mettre en cache
		await chrome.storage.local.set({
			[CACHE_KEY]: {
				data: webHosts,
				timestamp: now
			}
		});

		return webHosts;
	} catch (error) {
		console.error('[WebHost Links] Erreur lors de la récupération des webhosts:', error);
		return [];
	}
}

// Fonction pour extraire le domaine d'une URL
function extractDomain(url) {
	try {
		const urlObj = new URL(url);
		return urlObj.hostname;
	} catch (error) {
		return '';
	}
}

// Fonction pour trouver un webhost correspondant
function findMatchingWebHost(currentUrl, webHosts) {
	const currentDomain = extractDomain(currentUrl);

	for (const webHost of webHosts) {
		// Vérifier les URLs principales
		if (webHost.urls && webHost.urls.length > 0) {
			for (const urlObj of webHost.urls) {
				const webhostDomain = extractDomain(urlObj.url);
				if (webhostDomain === currentDomain) {
					return webHost;
				}
			}
		}

		// Vérifier les URLs associées
		if (webHost.associatedUrls && webHost.associatedUrls.length > 0) {
			for (const associatedUrl of webHost.associatedUrls) {
				const associatedDomain = extractDomain(associatedUrl.url);
				if (associatedDomain === currentDomain) {
					return webHost;
				}
			}
		}
	}

	return null;
}

// Fonction pour mettre à jour le badge d'un onglet
async function updateBadgeForTab(tabId) {
	try {
		const tab = await chrome.tabs.get(tabId);
		if (!tab.url || tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://')) {
			// Effacer le badge pour les pages système
			chrome.action.setBadgeText({ text: '', tabId: tabId });
			return;
		}

		const webHosts = await getCachedWebHosts();
		const matchingWebHost = findMatchingWebHost(tab.url, webHosts);

		if (matchingWebHost) {
			// Afficher un badge vert avec une coche
			chrome.action.setBadgeText({ text: '1', tabId: tabId });
			chrome.action.setBadgeBackgroundColor({ color: '#5C9DFF', tabId: tabId });
			chrome.action.setTitle({
				title: `WebHost Links - ${matchingWebHost.name} trouvé`,
				tabId: tabId
			});
		} else {
			// Effacer le badge si aucun webhost trouvé
			chrome.action.setBadgeText({ text: '', tabId: tabId });
			chrome.action.setTitle({
				title: 'WebHost Links - Aucun webhost trouvé pour cette URL',
				tabId: tabId
			});
		}
	} catch (error) {
		console.error('[WebHost Links] Erreur lors de la mise à jour du badge:', error);
		chrome.action.setBadgeText({ text: '', tabId: tabId });
	}
}

// Gérer les messages depuis la popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
	if (request.action === 'getCachedWebHosts') {
		getCachedWebHosts().then((webHosts) => {
			sendResponse({ webHosts });
		});
		return true; // Indique que la réponse sera asynchrone
	}

	if (request.action === 'clearCache') {
		chrome.storage.local.remove([CACHE_KEY]).then(() => {
			sendResponse({ success: true });
		});
		return true;
	}

	return false;
});
