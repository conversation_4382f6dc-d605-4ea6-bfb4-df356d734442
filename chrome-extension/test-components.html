<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test des Composants Lit</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .test-section h2 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        .component-demo {
            border: 1px dashed #ccc;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            background: #fafafa;
        }
        
        .code-block {
            background: #f8f8f8;
            border: 1px solid #e1e1e1;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>🧪 Test des Composants Lit - WebHost Links</h1>
    
    <div class="test-section">
        <h2>1. WebHost Card</h2>
        <div class="component-demo">
            <webhost-card id="webhost-test"></webhost-card>
        </div>
        <div class="code-block">
const webHostData = {
  name: "Example Website",
  environnement: "production",
  expectedVisibility: "public",
  gitlabActiveBranch: "main",
  lastCommitDate: "2024-01-15"
};
        </div>
    </div>

    <div class="test-section">
        <h2>2. Hosting Info</h2>
        <div class="component-demo">
            <hosting-info id="hosting-test"></hosting-info>
        </div>
        <div class="code-block">
const configData = {
  type: "docker",
  webId: "WEB001",
  server: "srv-prod-01"
};
        </div>
    </div>

    <div class="test-section">
        <h2>3. Tech Info</h2>
        <div class="component-demo">
            <tech-info id="tech-test"></tech-info>
        </div>
        <div class="code-block">
const serviceDetails = {
  php: {
    version: "8.2.15",
    is_latest: true,
    is_eoled: false
  },
  symfony: {
    version: "6.4.2",
    is_latest: false,
    is_eoled: false,
    latest_patch_version: "6.4.3"
  }
};
        </div>
    </div>

    <div class="test-section">
        <h2>4. Link Card</h2>
        <div class="component-demo">
            <link-card id="link-test"></link-card>
        </div>
        <div class="code-block">
const linkData = {
  type: "gitlab",
  title: "GitLab",
  url: "https://gitlab.example.com/project",
  originalUrl: "**********************:project.git"
};
        </div>
    </div>

    <div class="test-section">
        <h2>5. Links Grid</h2>
        <div class="component-demo">
            <links-grid id="links-test"></links-grid>
        </div>
        <div class="code-block">
const linksData = [
  { type: "gitlab", title: "GitLab", url: "https://gitlab.example.com" },
  { type: "confluence", title: "Documentation", url: "https://confluence.example.com" },
  { type: "database", title: "Base de données", url: "https://db.example.com" }
];
        </div>
    </div>

    <div class="test-section">
        <h2>6. Application Complète (Simulation)</h2>
        <div class="component-demo">
            <div style="width: 380px; margin: 0 auto;">
                <popup-app id="app-test"></popup-app>
            </div>
        </div>
        <div class="code-block">
// L'application complète avec données simulées
// Voir le code JavaScript ci-dessous
        </div>
    </div>

    <!-- Configuration simulée -->
    <script>
        window.CONFIG = {
            DEBUG: true,
            API_BASE_URL: 'https://api.example.com',
            API_ENDPOINT: '/webhosts',
            API_TIMEOUT: 5000
        };

        window.debugLog = function(...args) {
            console.log('[Test]', ...args);
        };

        // Simulation de l'API Chrome pour les tests
        window.chrome = {
            tabs: {
                query: () => Promise.resolve([{ url: 'https://example.com' }])
            },
            runtime: {
                sendMessage: (message) => {
                    if (message.action === 'getCachedWebHosts') {
                        return Promise.resolve({
                            webHosts: [{
                                name: "Example Website",
                                environnement: "production",
                                expectedVisibility: "public",
                                gitlabActiveBranch: "main",
                                lastCommitDate: "2024-01-15",
                                urls: [{ url: "https://example.com" }],
                                configuration: {
                                    type: "docker",
                                    webId: "WEB001",
                                    server: "srv-prod-01"
                                },
                                service: {
                                    id: "service-123",
                                    details: {
                                        php: {
                                            version: "8.2.15",
                                            is_latest: true,
                                            is_eoled: false
                                        },
                                        symfony: {
                                            version: "6.4.2",
                                            is_latest: false,
                                            is_eoled: false,
                                            latest_patch_version: "6.4.3"
                                        }
                                    },
                                    repository: {
                                        image: "php:8.2-fpm",
                                        tag: "8.2.15"
                                    },
                                    links: {
                                        logs: "https://swarmpit.example.com/logs"
                                    }
                                },
                                gitlabRemoteUrl: "**********************:project.git",
                                confluenceUrl: "https://confluence.example.com",
                                databaseUrl: "https://db.example.com"
                            }]
                        });
                    }
                    return Promise.resolve({});
                }
            }
        };
    </script>

    <!-- Import des composants -->
    <script type="module">
        // Import des composants
        import './components/webhost-card.js';
        import './components/hosting-info.js';
        import './components/tech-info.js';
        import './components/link-card.js';
        import './components/links-grid.js';
        import './components/popup-app.js';

        // Données de test
        const webHostData = {
            name: "Example Website",
            environnement: "production",
            expectedVisibility: "public",
            gitlabActiveBranch: "main",
            lastCommitDate: "2024-01-15"
        };

        const configData = {
            type: "docker",
            webId: "WEB001",
            server: "srv-prod-01"
        };

        const serviceDetails = {
            php: {
                version: "8.2.15",
                is_latest: true,
                is_eoled: false
            },
            symfony: {
                version: "6.4.2",
                is_latest: false,
                is_eoled: false,
                is_eomed: false,
                latest_patch_version: "6.4.3"
            }
        };

        const serviceRepository = {
            image: "php:8.2-fpm",
            tag: "8.2.15"
        };

        const linkData = {
            type: "gitlab",
            title: "GitLab",
            url: "https://gitlab.example.com/project",
            originalUrl: "**********************:project.git"
        };

        const linksData = [
            { type: "gitlab", title: "GitLab", url: "https://gitlab.example.com" },
            { type: "confluence", title: "Documentation", url: "https://confluence.example.com" },
            { type: "database", title: "Base de données", url: "https://db.example.com" },
            { type: "docker", title: "TDB Swarm", label: "Ouvrir dans TDB Swarm", url: "https://tdb-swarm.int.alienor.net#service-123" }
        ];

        // Attendre que les composants soient définis
        await customElements.whenDefined('webhost-card');
        await customElements.whenDefined('hosting-info');
        await customElements.whenDefined('tech-info');
        await customElements.whenDefined('link-card');
        await customElements.whenDefined('links-grid');
        await customElements.whenDefined('popup-app');

        // Configurer les composants de test
        document.getElementById('webhost-test').webHost = webHostData;
        document.getElementById('hosting-test').configuration = configData;
        
        const techTest = document.getElementById('tech-test');
        techTest.serviceDetails = serviceDetails;
        techTest.serviceRepository = serviceRepository;
        
        document.getElementById('link-test').link = linkData;
        document.getElementById('links-test').links = linksData;

        console.log('✅ Tous les composants sont chargés et configurés !');
    </script>
</body>
</html>
