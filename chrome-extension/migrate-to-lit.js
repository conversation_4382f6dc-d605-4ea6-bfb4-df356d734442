#!/usr/bin/env node

/**
 * Script de migration vers Lit
 * 
 * Usage:
 * node migrate-to-lit.js [--test|--deploy|--rollback]
 */

const fs = require('fs');
const path = require('path');

const MANIFEST_PATH = 'manifest.json';
const BACKUP_SUFFIX = '.backup';

function readManifest() {
    try {
        const content = fs.readFileSync(MANIFEST_PATH, 'utf8');
        return JSON.parse(content);
    } catch (error) {
        console.error('❌ Erreur lors de la lecture du manifest.json:', error.message);
        process.exit(1);
    }
}

function writeManifest(manifest) {
    try {
        fs.writeFileSync(MANIFEST_PATH, JSON.stringify(manifest, null, 2));
        console.log('✅ manifest.json mis à jour');
    } catch (error) {
        console.error('❌ Erreur lors de l\'écriture du manifest.json:', error.message);
        process.exit(1);
    }
}

function createBackup() {
    const backupPath = MANIFEST_PATH + BACKUP_SUFFIX;
    if (!fs.existsSync(backupPath)) {
        fs.copyFileSync(MANIFEST_PATH, backupPath);
        console.log('📦 Backup créé:', backupPath);
    }
}

function restoreBackup() {
    const backupPath = MANIFEST_PATH + BACKUP_SUFFIX;
    if (fs.existsSync(backupPath)) {
        fs.copyFileSync(backupPath, MANIFEST_PATH);
        console.log('🔄 Backup restauré');
        return true;
    }
    console.log('❌ Aucun backup trouvé');
    return false;
}

function switchToLit() {
    console.log('🚀 Migration vers Lit...');
    
    createBackup();
    
    const manifest = readManifest();
    
    if (manifest.action && manifest.action.default_popup) {
        const currentPopup = manifest.action.default_popup;
        
        if (currentPopup === 'popup-lit.html') {
            console.log('✅ Déjà configuré pour Lit');
            return;
        }
        
        manifest.action.default_popup = 'popup-lit.html';
        writeManifest(manifest);
        
        console.log('✅ Migration terminée !');
        console.log('📝 Popup changé de', currentPopup, 'vers popup-lit.html');
        console.log('');
        console.log('🔧 Prochaines étapes:');
        console.log('1. Rechargez l\'extension dans Chrome');
        console.log('2. Testez toutes les fonctionnalités');
        console.log('3. En cas de problème: node migrate-to-lit.js --rollback');
    } else {
        console.log('❌ Structure de manifest.json non reconnue');
    }
}

function switchToOriginal() {
    console.log('🔄 Retour à la version originale...');
    
    const manifest = readManifest();
    
    if (manifest.action && manifest.action.default_popup) {
        const currentPopup = manifest.action.default_popup;
        
        if (currentPopup === 'popup.html') {
            console.log('✅ Déjà configuré pour la version originale');
            return;
        }
        
        manifest.action.default_popup = 'popup.html';
        writeManifest(manifest);
        
        console.log('✅ Retour terminé !');
        console.log('📝 Popup changé de', currentPopup, 'vers popup.html');
    }
}

function testMode() {
    console.log('🧪 Mode test activé');
    console.log('');
    
    // Vérifier que tous les fichiers nécessaires existent
    const requiredFiles = [
        'popup-lit.html',
        'components/popup-app.js',
        'components/webhost-card.js',
        'components/hosting-info.js',
        'components/tech-info.js',
        'components/link-card.js',
        'components/links-grid.js',
        'services/webhost-service.js'
    ];
    
    let allFilesExist = true;
    
    console.log('📁 Vérification des fichiers:');
    requiredFiles.forEach(file => {
        if (fs.existsSync(file)) {
            console.log('✅', file);
        } else {
            console.log('❌', file, '(manquant)');
            allFilesExist = false;
        }
    });
    
    console.log('');
    
    if (allFilesExist) {
        console.log('✅ Tous les fichiers sont présents');
        console.log('🚀 Vous pouvez lancer la migration avec: node migrate-to-lit.js --deploy');
        console.log('');
        console.log('🧪 Pour tester les composants individuellement:');
        console.log('   Ouvrez test-components.html dans votre navigateur');
    } else {
        console.log('❌ Des fichiers sont manquants');
        console.log('📝 Assurez-vous d\'avoir tous les composants Lit');
    }
    
    // Vérifier la configuration actuelle
    const manifest = readManifest();
    const currentPopup = manifest.action?.default_popup || 'non défini';
    console.log('');
    console.log('⚙️  Configuration actuelle:');
    console.log('   Popup:', currentPopup);
    
    if (currentPopup === 'popup-lit.html') {
        console.log('   Status: 🟢 Version Lit active');
    } else if (currentPopup === 'popup.html') {
        console.log('   Status: 🟡 Version originale active');
    } else {
        console.log('   Status: 🔴 Configuration inconnue');
    }
}

function showHelp() {
    console.log('🔧 Script de migration vers Lit');
    console.log('');
    console.log('Usage:');
    console.log('  node migrate-to-lit.js --test      # Vérifier les prérequis');
    console.log('  node migrate-to-lit.js --deploy    # Migrer vers Lit');
    console.log('  node migrate-to-lit.js --rollback  # Retour à la version originale');
    console.log('');
    console.log('Exemples:');
    console.log('  # Tester avant migration');
    console.log('  node migrate-to-lit.js --test');
    console.log('');
    console.log('  # Migrer vers Lit');
    console.log('  node migrate-to-lit.js --deploy');
    console.log('');
    console.log('  # En cas de problème, revenir en arrière');
    console.log('  node migrate-to-lit.js --rollback');
}

// Main
const args = process.argv.slice(2);
const command = args[0];

console.log('🎯 WebHost Links - Migration Lit');
console.log('================================');
console.log('');

switch (command) {
    case '--test':
        testMode();
        break;
        
    case '--deploy':
        switchToLit();
        break;
        
    case '--rollback':
        if (restoreBackup()) {
            switchToOriginal();
        }
        break;
        
    case '--help':
    case '-h':
        showHelp();
        break;
        
    default:
        if (command) {
            console.log('❌ Commande inconnue:', command);
            console.log('');
        }
        showHelp();
        break;
}

console.log('');
