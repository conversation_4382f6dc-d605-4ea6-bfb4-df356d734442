import { LitElement, html, css } from 'https://cdn.skypack.dev/lit@3';

export class HostingInfo extends LitElement {
  static properties = {
    configuration: { type: Object }
  };

  static styles = css`
    .hosting-info {
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      padding: 12px;
      margin-bottom: 16px;
    }

    .hosting-title {
      font-size: 12px;
      font-weight: 600;
      color: #495057;
      margin-bottom: 8px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .hosting-details {
      display: grid;
      gap: 6px;
    }

    .hosting-item {
      display: flex;
      align-items: center;
      font-size: 11px;
    }

    .hosting-label {
      font-weight: 500;
      color: #6c757d;
      min-width: 60px;
      margin-right: 8px;
    }

    .hosting-value {
      color: #495057;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      background: #e9ecef;
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 10px;
    }
  `;

  render() {
    if (!this.configuration) return html``;

    return html`
      <div class="hosting-info">
        <div class="hosting-title">🏠 Hébergement</div>
        <div class="hosting-details">
          ${this._renderHostingItems()}
        </div>
      </div>
    `;
  }

  _renderHostingItems() {
    const items = [];

    if (this.configuration.type) {
      items.push(html`
        <div class="hosting-item">
          <span class="hosting-label">Type:</span>
          <span class="hosting-value">${this.configuration.type.toUpperCase()}</span>
        </div>
      `);
    }

    if (this.configuration.webId) {
      items.push(html`
        <div class="hosting-item">
          <span class="hosting-label">Web ID:</span>
          <span class="hosting-value">${this.configuration.webId}</span>
        </div>
      `);
    }

    if (this.configuration.server) {
      items.push(html`
        <div class="hosting-item">
          <span class="hosting-label">Serveur:</span>
          <span class="hosting-value">${this.configuration.server}</span>
        </div>
      `);
    }

    return items;
  }
}

customElements.define('hosting-info', HostingInfo);
