import { LitElement, html, css } from 'https://cdn.skypack.dev/lit@3';
import './link-card.js';

export class LinksGrid extends LitElement {
  static properties = {
    links: { type: Array }
  };

  static styles = css`
    .links-grid {
      display: grid;
      gap: 4px;
      margin-bottom: 16px;
    }
  `;

  render() {
    if (!this.links || this.links.length === 0) return html``;

    return html`
      <div class="links-grid">
        ${this.links.map(link => html`
          <link-card .link=${link}></link-card>
        `)}
      </div>
    `;
  }
}

customElements.define('links-grid', LinksGrid);
