#syntax=docker/dockerfile:1.4

# Versions
FROM dunglas/frankenphp:1-php8.4 AS frankenphp_upstream

# Base FrankenPHP image
FROM frankenphp_upstream AS frankenphp_base

WORKDIR /var/www/html

VOLUME /var/www/html/var/

# persistent / runtime deps
# hadolint ignore=DL3008
RUN apt-get update && apt-get install -y --no-install-recommends \
	acl \
	file \
	gettext \
	git \
    sudo \
    micro \
	&& rm -rf /var/lib/apt/lists/*

RUN set -eux; \
	install-php-extensions \
		@composer \
        pdo_pgsql \
        gd \
		intl \
		opcache \
		zip \
        zstd \
        sockets\
        pcntl \
        redis \
	;

# https://getcomposer.org/doc/03-cli.md#composer-allow-superuser
ENV COMPOSER_ALLOW_SUPERUSER=1

COPY --link frankenphp/conf.d/10-app.ini $PHP_INI_DIR/conf.d/
COPY --link --chmod=755 frankenphp/docker-entrypoint.sh /usr/local/bin/docker-entrypoint
COPY --link frankenphp/Caddyfile /etc/caddy/Caddyfile

ENTRYPOINT ["docker-entrypoint"]

HEALTHCHECK --start-period=60s CMD curl -f http://localhost:2019/metrics || exit 1
CMD [ "frankenphp", "run", "--config", "/etc/caddy/Caddyfile" ]

################################################ COMMON ################################################################

ARG APACHE_RUN_USER=www-data
ARG APACHE_RUN_GROUP=www-data
ARG UID_USER=1000
ARG GID_USER=1000

RUN usermod -u ${UID_USER} ${APACHE_RUN_USER} && groupmod -g ${GID_USER} ${APACHE_RUN_GROUP}

RUN echo 'alias sf="sudo -HEu www-data php bin/console"\n\
alias svi="sudo -HEu www-data vi"\n\
alias svi="sudo -u www-data vi"\n\
alias sw="sudo -HEu www-data"\n\
alias sf="sudo -HEu www-data php bin/console"' >> ~/.bashrc

RUN chown -R www-data:www-data /var/www

### ADMINER
RUN mkdir -p /var/www/_adminer
RUN curl -L "https://github.com/vrana/adminer/releases/download/v5.3.0/adminer-5.3.0.php" -o /var/www/_adminer/index.php
RUN chown -R ${APACHE_RUN_USER}:${APACHE_RUN_GROUP} /var/www/_adminer


### ANET VERSION
ARG SWARM_CHECKER_VERSION="1.0.4"
ARG SWARM_CHECKER_URL="https://gitlab.alienor.net/api/v4/projects/dev-interne%2Ftdb-docker-swarm/packages/generic/swarm-checker/${SWARM_CHECKER_VERSION}"
RUN mkdir -p /var/www/_anetversion
RUN curl "${SWARM_CHECKER_URL}/swarm-checker.phar" --output /var/www/_anetversion/swarm-checker.phar && \
    curl "${SWARM_CHECKER_URL}/checker.php" --output /var/www/_anetversion/checker.php && \
    chmod 755 /var/www/_anetversion/swarm-checker.phar && \
    chmod 644 /var/www/_anetversion/checker.php && \
    /var/www/_anetversion/swarm-checker.phar --install
RUN echo "<?php phpinfo(); ?>" > /var/www/_anetversion/index.php
RUN chown -R ${APACHE_RUN_USER}:${APACHE_RUN_GROUP} /var/www/_anetversion

################################################ DEV ###################################################################

FROM frankenphp_base AS frankenphp_dev

ENV APP_ENV=dev XDEBUG_MODE=off

RUN mv "$PHP_INI_DIR/php.ini-development" "$PHP_INI_DIR/php.ini"

RUN set -eux; \
	install-php-extensions \
		xdebug \
	;

COPY --link frankenphp/conf.d/20-app.dev.ini $PHP_INI_DIR/conf.d/

CMD [ "frankenphp", "run", "--config", "/etc/caddy/Caddyfile", "--watch" ]

################################################ PROD ##################################################################

FROM frankenphp_base AS frankenphp_prod_composer

ENV APP_ENV=prod
#ENV FRANKENPHP_CONFIG="import worker.Caddyfile"

ARG UID_USER=33
ARG GID_USER=33

RUN usermod -u ${UID_USER} ${APACHE_RUN_USER} && groupmod -g ${GID_USER} ${APACHE_RUN_GROUP}

RUN mv "$PHP_INI_DIR/php.ini-production" "$PHP_INI_DIR/php.ini"

COPY --link frankenphp/conf.d/20-app.prod.ini $PHP_INI_DIR/conf.d/
COPY --link frankenphp/worker.Caddyfile /etc/caddy/worker.Caddyfile

# prevent the reinstallation of vendors at every changes in the source code
COPY --link --chown=www-data:www-data composer.* symfony.* ./
RUN set -eux; \
	sudo -u www-data composer install --no-cache --prefer-dist --no-autoloader --no-scripts --no-progress

# copy sources
COPY --link --chown=www-data:www-data . ./
RUN rm -Rf frankenphp/

RUN set -eux; \
	mkdir -p var/cache var/log; \
	chown -R www-data:www-data var; \
	sudo -u www-data composer dump-autoload --classmap-authoritative; \
	sudo -u www-data composer run-script post-install-cmd; \
	chmod +x bin/console; sync;

# Node stage
FROM node:20-alpine AS node_build

COPY --link --from=frankenphp_prod_composer /var/www/html/package*.json /app/
COPY --link --from=frankenphp_prod_composer /var/www/html/tsconfig.json /app/
COPY --link --from=frankenphp_prod_composer /var/www/html/tsconfig.node.json /app/
COPY --link --from=frankenphp_prod_composer /var/www/html/vendor /app/vendor

WORKDIR /app

RUN yarn install --force

COPY --link --from=frankenphp_prod_composer /var/www/html/assets /app/assets
COPY --link --from=frankenphp_prod_composer /var/www/html/vite.config.ts /app/

RUN yarn build

# Prod image
FROM frankenphp_prod_composer as frankenphp_prod

COPY --from=node_build --link --chown=www-data:www-data /app/public/build /var/www/html/public/build/