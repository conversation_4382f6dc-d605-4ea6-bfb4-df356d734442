<script lang="ts">
	import { groupBy, sumBy, find } from 'lodash';
	import { sort } from 'fast-sort';
	import ImageCopyButton from './ImageCopyButton.svelte';
	interface Props {
		services?: Service[];
	}

	let { services = [] }: Props = $props();

	let stats = $state([]);

	type StatRow = {
		phpVersionMajor: string;
		phpVersionMinor: string;
		phpBuildAt: string;
		imageFull: string;
		image: string;
		tag: string;
		count: number;
		mergeMajor?: number;
		mergeMinor?: number;
	};

	const columns = [
		{
			id: 'phpVersionMajor',
			name: 'Version Majeur'
		},
		{
			id: 'phpVersionMinor',
			name: 'Version mineur'
		},
		{
			id: 'tag',
			name: 'Tags'
		}
	];

	$effect(() => {
		let tmpStats: StatRow[] = [];
		const phpServices = services.filter((s) => s.redeployedAllowed);

		const byImages = groupBy(phpServices, (s) => s.repository.image);
		for (const image in byImages) {
			let byHashes = groupBy(byImages[image], (s) => s.repository.imageDigest);
			for (const hash in byHashes) {
				let hasPhpVersion = <Service>(
					find(byHashes[hash], (s: Service) => typeof s?.details?.php?.version !== 'undefined')
				);
				let phpVersionMinor = null;
				let phpVersionMajor = null;
				let phpBuildAt = null;
				if (hasPhpVersion) {
					phpVersionMinor = hasPhpVersion.details?.php?.version;
					phpVersionMajor = phpVersionMinor.substring(0, phpVersionMinor.lastIndexOf('.'));
					phpBuildAt = hasPhpVersion.details?.php?.buildDate
						? new Date(hasPhpVersion.details.php.buildDate).toLocaleDateString('fr-FR', {
								day: '2-digit',
								month: '2-digit',
								year: '2-digit'
							})
						: null;
				} else {
					phpVersionMajor = image?.match(/web_php(-.*)?:(.*)-/)?.[2];
				}
				tmpStats.push({
					phpVersionMinor: phpVersionMinor,
					phpVersionMajor: phpVersionMajor,
					phpBuildAt: phpBuildAt,
					imageFull: image,
					image: image.substring(image.lastIndexOf('/') + 1),
					tag: '#' + hash.slice(-8),
					count: byHashes[hash].length
				});
			}
		}

		tmpStats = tmpStats.filter((t) => {
			return (
				t.phpVersionMajor !== 'undefined' &&
				t.phpVersionMinor !== 'undefined' &&
				t.phpVersionMajor !== '' &&
				t.phpVersionMinor !== '' &&
				t.phpVersionMajor !== null &&
				t.phpVersionMinor !== null
			);
		});

		const byMajor = groupBy(tmpStats, 'phpVersionMajor');

		const nestedStats = {};

		for (const version in byMajor) {
			const byMinor = groupBy(byMajor[version], 'phpVersionMinor');
			const tmpMinors = {};
			for (const subVersion in byMinor) {
				tmpMinors[subVersion] = {
					version: subVersion,
					count: sumBy(byMinor[subVersion], 'count'),
					mergeMinor: byMinor[subVersion].length,
					tags: sort(Object.values(byMinor[subVersion])).desc((t) => t.image)
				};
			}

			nestedStats[version] = {
				version,
				count: sumBy(byMajor[version], 'count'),
				mergeMajor: Object.values(tmpMinors).length,
				minor: sort(Object.values(tmpMinors)).desc((s) => s.version)
			};
		}

		stats = sort(Object.values(nestedStats)).desc((s) => s.version);
	});
</script>

<div class="table-responsive my-5">
	<h5 class="mb-3">Statistiques</h5>

	<div class="gridjs-wrapper" style="height: auto">
		<table role="grid" class="gridjs-table" style="min-width: 100%; height: auto">
			<thead class="gridjs-head">
				<tr class="gridjs-tr">
					{#each columns as column}
						<th class="gridjs-th gridjs-th-sort">
							<div class="gridjs-th-content">{column.name}</div>
						</th>
					{/each}
				</tr>
			</thead>
			<tbody class="gridjs-tbody">
				{#each stats as row}
					{#each row.minor as minor, i}
						<tr class="gridjs-tr">
							{#if i === 0}
								<td class="gridjs-td" rowspan={row.mergeMajor}>
									{row.version} <span class="badge rounded-pill bg-secondary">{row.count}</span>
								</td>
							{/if}
							<td class="gridjs-td">
								{minor.version} <span class="badge rounded-pill bg-secondary">{minor.count}</span>
							</td>
							<td class="gridjs-td">
								{#each minor.tags as tag}
									<div>
										<ImageCopyButton title={tag.imageFull} />
										{tag.image.replace('web_php:', '').replace('web_php-', '')}
										{tag.tag}
										{#if tag.phpBuildAt}{tag.phpBuildAt}
										{/if} <span class="badge rounded-pill bg-secondary">{tag.count}</span>
									</div>
								{/each}
							</td>
						</tr>
					{/each}
				{/each}
			</tbody>
		</table>
	</div>
</div>
