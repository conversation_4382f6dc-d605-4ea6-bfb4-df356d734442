<script lang="ts">
	import { flatMap } from 'lodash';

	interface Props {
		service: Service;
	}

	let { service }: Props = $props();

	let length = $derived.by(() => {
		if (service.details && service.details['local-php-security-checker']?.advisories) {
			return flatMap(service.details['local-php-security-checker']?.advisories).length;
		}
		return 0;
	});
</script>

{#if service.details && service.details['local-php-security-checker']}
	<div class="text-center fw-bold">
		{#if length}
			<i class="fa-solid fa-shield-xmark text-danger me-1"></i>{length}
		{:else}
			<i class="fa-solid fa-shield-check text-success"></i>
		{/if}
	</div>
{:else}
	<div class="text-end">{service.redeployedAllowed && service.details === null ? '…' : ''}</div>
{/if}
