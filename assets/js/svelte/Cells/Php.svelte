<script lang="ts">
	interface Props {
		service: Service;
	}

	let { service }: Props = $props();

	let color = $derived.by(() => {
		let c = '';

		if (service.redeployedAllowed) {
			c = 'secondary';

			if (service?.details?.php?.latest_patch_version) {
				c = 'success';
				if (!service?.details?.php?.is_latest) {
					c = 'warning text-dark';
				}
				if (service?.details?.php?.is_eoled) {
					c = 'danger';
				}
			}
		}

		return c;
	});
</script>

{#if service?.details?.php?.version}
	<div class="text-end"><span class="badge bg-{color}">{service.details.php.version}</span></div>
{:else if service.details}
	<div class="text-end"></div>
{:else}
	<div class="text-end">…</div>
{/if}
