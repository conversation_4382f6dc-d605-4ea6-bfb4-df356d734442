<script lang="ts">
	interface Props {
		service: Service;
	}

	let { service }: Props = $props();
	let tooltip: string | boolean = $state('');
	let icon = $state('');
	$effect(() => {
		let tooltipRows = [];
		if (service.middleware) {
			tooltipRows.push(service.middleware);
			icon = 'fa-lock';
		}
		if (service?.details?.htaccess?.requireUser) {
			tooltipRows.push(`htaccess : ${service?.details?.htaccess?.requireUser.join(', ')}`);
			icon = 'fa-lock-hashtag';
		}
		tooltip = tooltipRows.length ? tooltipRows.join('\n') : false;
	});
</script>

{#each service.urls as url}
	<div class="text-nowrap">
		{#if tooltip}
			<i class="fa {icon}" data-bs-toggle="tooltip" title={tooltip}></i>
		{/if}
		<a target="_blank" href={url}>{url.replace(/^https?:\/\//, '')}</a>
	</div>
{/each}
{#if service?.details?.portail?.webserviceUrl}
	<div class="text-nowrap">
		<span class="fw-light" style="font-size: 14px">WS :</span>
		<a target="_blank" href={service?.details?.portail?.webserviceUrl}
			>{service?.details?.portail?.webserviceUrl.replace(/^https?:\/\//, '')}</a
		>
	</div>
{/if}
