<script lang="ts">
	import ImageCopyButton from '../ImageCopyButton.svelte';
	import { getImageShortName } from '../../utils';

	interface Props {
		service: Service;
	}

	let { service }: Props = $props();
	let digest = $derived(
		service.repository.imageDigest ? service.repository.imageDigest.slice(-8) : null
	);
</script>

<div class="text-end">
	{getImageShortName(service.repository.image)}
	<ImageCopyButton title={service.repository.image} />
	<br />
	#{digest}
</div>
