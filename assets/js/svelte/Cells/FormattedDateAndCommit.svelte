<script lang="ts">
	interface Props {
		service: Service;
		date: string | undefined;
	}

	let { date, service }: Props = $props();

	let dateString = $derived.by(() => {
		return date
			? new Date(date).toLocaleDateString('fr-FR', {
					day: '2-digit',
					month: '2-digit',
					year: '2-digit'
				})
			: null;
	});

	let commit = $derived(
		service?.details?.git?.lastCommit ? '#' + service.details.git.lastCommit.slice(0, 8) : null
	);
</script>

{#if dateString}
	<div class="text-end">
		{dateString}
		{#if commit}<br />{commit}{/if}
	</div>
{/if}
