<script lang="ts">
	interface Props {
		service: Service;
		onclick?: (stack: string) => void;
		services: () => Service[];
	}

	let { service, onclick = () => {}, services = () => [] }: Props = $props();

	const servicesInStack = $derived(
		services()?.filter((s) => s.stack === service.stack && s.server === service.server).length
	);
</script>

<div>
	<span class="badge bg-secondary" onclick={() => onclick(service.stack)} style="cursor: pointer">
		<i class="fa fa-magnifying-glass"></i>
		{servicesInStack}
	</span>
	{service.stack}
</div>
