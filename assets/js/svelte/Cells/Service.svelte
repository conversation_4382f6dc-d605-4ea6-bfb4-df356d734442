<script lang="ts">
	interface Props {
		service: Service;
	}

	let { service }: Props = $props();
	let icon = $state('fa-square-terminal');

	let showDockerTools = localStorage.getItem('showDockerTools') === 'true';

	const copy = (e: Event) => {
		e.preventDefault();
		navigator.clipboard.writeText(service.tasks[0].sshCommand);
		icon = 'fa-clipboard-check';
		setTimeout(() => {
			icon = 'fa-square-terminal';
		}, 500);
	};
</script>

<div class="d-flex align-items-center">
	{#if showDockerTools}
		<a href={service.tasks[0].dockerToolsCommand}>
			<i role="button" class="pe-1 fs-4 text-info fa-solid {icon}"></i>
		</a>
	{:else}
		<i role="button" class="pe-1 fs-4 text-info fa-solid {icon}" onclick={copy}></i>
	{/if}

	<a target="_blank" href={service.links.service}>{service.name}</a>
</div>
