<script lang="ts">
	import { preventDefault } from 'svelte/legacy';

	import { getService, redeployService } from '../../api';
	import { waitingForUpdates } from '../stores';

	interface Props {
		service: Service;
		showDetails?: any;
		showTags?: any;
	}

	let { service, showDetails = (service) => {}, showTags = (service) => {} }: Props = $props();
	const deploying = $derived(service.status.update === 'updating');

	async function redeploy(e: Event) {
		e.preventDefault();
		if (
			confirm(
				`Êtes-vous sur de vouloir rédéployer le service "${service.stack} - ${service.name}" ?`
			)
		) {
			setWaiting('redeploy');
			await redeployService(service.server, service.id);
		}
	}

	async function check(e: Event) {
		e.preventDefault();
		setWaiting('refresh');
		await getService(service.server, service.id);
	}

	function setWaiting(type: 'refresh' | 'redeploy') {
		$waitingForUpdates = [
			{
				type,
				serviceId: service.id,
				serverName: service.server,
				waiting: true
			}
		];
	}
</script>

<div class="text-nowrap text-end">
	<button
		class="btn btn-sm btn-info text-white"
		class:btn-info={!service.redeployedAllowed ||
			(service.redeployedAllowed && service.details !== null)}
		class:btn-secondary={service.redeployedAllowed && service.details === null}
		onclick={() => showDetails(service)}><i class="fa fa-info-circle"></i></button
	>
	<button
		onclick={check}
		class="btn btn-sm btn-info text-white"
		data-bs-toggle="tooltip"
		title="Forcer la mise à jour les données de la ligne"
		><i class="fa fa-cloud-arrow-down"></i></button
	>
	{#if service.redeployedAllowed}
		<button
			onclick={preventDefault(() => showTags(service))}
			class="btn btn-sm btn-danger"
			data-bs-toggle="tooltip"
			title="Modifier le tag"
			><i class="fa fa-tag"></i>
		</button>
		<button
			onclick={redeploy}
			class="btn btn-sm btn-danger"
			data-bs-toggle="tooltip"
			title="Redeployer le service"
			><i class="fa fa-refresh" class:fa-spin={deploying}></i>
		</button>
	{/if}
</div>
