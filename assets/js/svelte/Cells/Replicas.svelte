<script lang="ts">
	import TaskSquare from '../TaskSquare.svelte';
	interface Props {
		service: Service;
	}

	let { service }: Props = $props();
	let icon = $derived(
		service.status.tasks.running === service.status.tasks.total ? 'check' : 'xmark'
	);
	let color = $derived(
		service.status.tasks.running === service.status.tasks.total ? 'success' : 'danger'
	);
</script>

<div class="text-end">
	<div class="text-center">
		<i class="fa-solid fa-circle-{icon} text-{color}"></i>
		{service.status.tasks.running} / {service.status.tasks.total}
	</div>
	<div class="text-center">
		{#each service.tasks as task (task.id)}
			<TaskSquare {task} {service} />
		{/each}
	</div>
</div>
