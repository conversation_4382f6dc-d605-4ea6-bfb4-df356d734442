import { writable } from 'svelte/store';
import type { Writable } from 'svelte/store';

const url = JSON.parse(document.getElementById('mercure-url').textContent);
export const mercureEventSource = writable(new EventSource(url));

export const selectedServices: Writable<Service[]> = writable([]);
export const waitingForUpdates: Writable<WaitingService[]> = writable([]);

export const selectedWebHosts: Writable<WebHost[]> = writable([]);
export const waitingForWebHostUpdates: Writable<WaitingWebHost[]> = writable([]);
