<script lang="ts">
	interface Props {
		webHost: WebHost;
	}

	let { webHost }: Props = $props();

	let overallSSLStatus = $derived.by(() => {
		const statuses = webHost.urls
			.filter((url) => url.sslCertificateReport?.isValid !== null)
			.map((url) => url.sslCertificateReport.isValid);
		return statuses.length === 0 ? null : !statuses.includes(false);
	});
</script>

{#if overallSSLStatus !== null}
	<span class="badge bg-white">
		{#if overallSSLStatus === false}
			<i class="fa-solid fa-ban text-danger"></i>
		{:else}
			<i class="fa-solid fa-circle-check text-success"></i>
		{/if}
	</span>
{/if}
