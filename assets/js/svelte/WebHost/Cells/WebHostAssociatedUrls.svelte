<script lang="ts">
	interface Props {
		webHost: WebHost;
	}

	let { webHost }: Props = $props();

	// gitlab
	let gitRemoteUrl = null;
	let isGitwebRemote = false;
	let gitlabLinkTitle = null;
	if (webHost.gitlabRemoteUrl) {
		if (webHost.gitlabRemoteUrl.includes('**********************:')) {
			const remote = webHost.gitlabRemoteUrl
				.replace('**********************:', '')
				.replace('.git', '');
			gitRemoteUrl = `https://gitlab.alienor.net/${remote}`;
		} else if (webHost.gitlabRemoteUrl.includes('https://gitlab.alienor.net/')) {
			gitRemoteUrl = webHost.gitlabRemoteUrl;
		}
		isGitwebRemote =
			webHost.gitlabRemoteUrl.includes('ssh://<EMAIL>') ||
			webHost.gitlabRemoteUrl.includes('ssh://<EMAIL>');
		gitlabLinkTitle = `${webHost.gitlabRemoteUrl}:${webHost.gitlabActiveBranch || ''}`;
	}

	function handleGitwebClick() {
		alert("Gitweb n'existe plus");
	}
</script>

<div class="d-flex align-items-center gap-1">
	{#if webHost.gitlabRemoteUrl}
		{#if gitRemoteUrl}
			<a
				target="_blank"
				class="d-flex align-items-center associated-url"
				href={gitRemoteUrl}
				title={gitlabLinkTitle}
			>
				<img src="/images/gitlab.png" width="16px" height="16px" alt="Gitlab" />
			</a>
		{:else if isGitwebRemote}
			<span title={gitlabLinkTitle} onclick={handleGitwebClick} class="associated-url">
				<img src="/images/gitweb.png" width="16px" height="16px" alt="Gitweb" />
			</span>
		{:else}
			{webHost.gitlabRemoteUrl}
		{/if}
	{/if}
	{#if webHost.confluenceUrl}
		<a target="_blank" class="associated-url" href={webHost.confluenceUrl}>
			<img src="/images/confluence.ico" width="16px" height="16px" alt="Confluence" />
		</a>
	{/if}
	{#if webHost.databaseUrl}
		<a target="_blank" class="associated-url" href={webHost.databaseUrl}>
			<i class="fa-solid fa-database"></i>
		</a>
	{/if}
	{#each webHost.associatedUrls as associatedUrl}
		<a target="_blank" class="associated-url" href={associatedUrl.url}>
			{#if associatedUrl.favicon}
				<img src={associatedUrl.favicon} width="16px" height="16px" />
			{:else}
				<i class="fa-solid fa-link"></i>
			{/if}
		</a>
	{/each}
</div>
