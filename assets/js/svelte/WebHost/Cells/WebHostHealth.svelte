<script lang="ts">
	interface Props {
		webHost: WebHost;
	}

	let { webHost }: Props = $props();

	let overallStatus = $derived.by(() => {
		const statuses = webHost.urls
			.filter((status) => status !== null && status !== undefined)
			.map((url) => url.healthCheckReport?.status);
		if (statuses.length === 0) return null;
		if (statuses.includes('CRITICAL')) return 'CRITICAL';
		if (statuses.includes('WARNING')) return 'WARNING';
		return 'OK';
	});
</script>

{#if overallStatus}
	<span class="badge bg-white">
		{#if overallStatus === 'CRITICAL'}
			<i class="fa-solid fa-ban text-danger"></i>
		{:else if overallStatus === 'WARNING'}
			<i class="fa-solid fa-triangle-exclamation text-warning"></i>
		{:else}
			<i class="fa-solid fa-circle-check text-success"></i>
		{/if}
	</span>
{/if}
