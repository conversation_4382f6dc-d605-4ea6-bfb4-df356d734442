<script lang="ts">
	import type { Row } from '@tanstack/svelte-table';

	interface Props {
		webHost: WebHost;
		row: Row<WebHost>;
	}

	let { webHost, row }: Props = $props();

	// Variable réactive pour suivre l'état d'expansion
	let isExpanded = $state(row.getIsExpanded());

	const toggleExpanded = () => {
		row.toggleExpanded();
		// Mettre à jour la variable réactive
		isExpanded = !row.getIsExpanded();
	};
</script>

{#if webHost.urls.length > 1}
	<button class="btn btn-sm btn-transparent" type="button" onclick={toggleExpanded}>
		{#if isExpanded}
			<i class="fa-regular fa-square-minus"></i>
		{:else}
			<i class="fa-regular fa-square-plus"></i>
		{/if}
	</button>
{/if}
