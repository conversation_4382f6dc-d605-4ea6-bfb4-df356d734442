<script lang="ts">
	interface Props {
		webHost: WebHost;
	}

	let { webHost }: Props = $props();
</script>

{#if webHost.environnement}
	{#if webHost.environnement === 'dev'}
		<span class="badge bg-light text-dark">développement</span>
	{:else if webHost.environnement === 'preprod'}
		<span class="badge bg-secondary">preproduction</span>
	{:else if webHost.environnement === 'prod'}
		<span class="badge bg-success">production</span>
	{/if}
{/if}
