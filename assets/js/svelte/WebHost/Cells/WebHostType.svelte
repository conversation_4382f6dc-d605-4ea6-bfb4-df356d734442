<script lang="ts">
	interface Props {
		webHost: WebHost;
	}

	let { webHost }: Props = $props();
</script>

{#if webHost.configuration?.type === 'vm'}
	<i class="fa-solid fa-server" title="Machine virtuelle"></i>
{:else if webHost.configuration?.type === 'swarm'}
	<a href="/front?search={webHost.configuration.stack}">
		<img
			src="/images/docker.png"
			alt="Docker Swarm"
			width="20px"
			height="20px"
			title="Docker Swarm"
		/>
	</a>
{/if}
