<script lang="ts">
	import { selectedWebHosts, waitingForWebHostUpdates } from '../../stores';

	interface Props {
		webHost: WebHost;
	}

	let { webHost }: Props = $props();

	let isSelected = $derived($selectedWebHosts.some((s) => s.id === webHost.id));
	let isWaiting = $derived(
		$waitingForWebHostUpdates.some((w) => w.webHostId === webHost.id && w.waiting)
	);
	let hasError = $derived(
		$waitingForWebHostUpdates.some((w) => w.webHostId === webHost.id && w.error)
	);

	const toggleSelection = () => {
		const index = $selectedWebHosts.findIndex((s) => s.id === webHost.id);
		if (index === -1) {
			$selectedWebHosts = [...$selectedWebHosts, webHost];
		} else {
			$selectedWebHosts = $selectedWebHosts.filter((s) => s.id !== webHost.id);
		}
	};
</script>

<div class="text-center">
	{#if isWaiting}
		<i class="text-info fa-solid fa-circle-notch fa-spin"></i>
	{:else if hasError}
		<i class="text-danger fa-solid fa-triangle-exclamation"></i>
	{:else}
		<input
			class="form-check-input"
			type="checkbox"
			checked={isSelected}
			onchange={toggleSelection}
		/>
	{/if}
</div>
