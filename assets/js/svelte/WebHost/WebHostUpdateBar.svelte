<script lang="ts">
	import { onMount } from 'svelte';
	import { fade } from 'svelte/transition';
	import WebHostWaitingProgress from './WebHostWaitingProgress.svelte';
	import { refreshWebHosts } from '../../api';
	import { waitingForWebHostUpdates, selectedWebHosts, mercureEventSource } from '../stores';

	let eventSourceInitialized = false;

	onMount(() => {
		if (!eventSourceInitialized) {
			$mercureEventSource.addEventListener('message', (event) => {
				let message = JSON.parse(event.data);
				const waitingKey = $waitingForWebHostUpdates.findIndex(
					(webHost) => webHost.webHostId === message.data.id
				);

				if (waitingKey !== -1 && message.topic === 'webhost.updated') {
					$waitingForWebHostUpdates[waitingKey].waiting = false;
				}
			});
			eventSourceInitialized = true;
		}
	});

	async function refreshBatches(e) {
		e.preventDefault();
		let count = $selectedWebHosts.length;
		let multiple = $selectedWebHosts.length > 1;
		if (
			confirm(
				`Êtes-vous sûr de vouloir actualiser le${multiple ? `s ${count}` : ''} hébergement${multiple ? 's' : ''} sélectionné${multiple ? 's' : ''} ?`
			)
		) {
			initWaitingList('refresh');
			await refreshWebHosts(
				$selectedWebHosts.map((wh) => ({
					webHostId: wh.id
				}))
			);
		}
	}

	async function initWaitingList(type: 'refresh') {
		$waitingForWebHostUpdates = $selectedWebHosts.map((wh) => ({
			type,
			webHostId: wh.id,
			waiting: true
		}));
	}

	async function endWaiting() {
		await new Promise((resolve) => setTimeout(resolve, 1000));
		$waitingForWebHostUpdates = [];
	}
</script>

{#if $selectedWebHosts.length}
	<div transition:fade class="d-flex align-items-center">
		<WebHostWaitingProgress />
		<div class="me-3">
			{$selectedWebHosts.length} hébergement{$selectedWebHosts.length > 1 ? 's' : ''} sélectionné{$selectedWebHosts.length >
			1
				? 's'
				: ''}
		</div>
		<button
			class="btn btn-sm btn-info text-white me-2"
			disabled={$waitingForWebHostUpdates.length > 0}
			onclick={refreshBatches}
		>
			<i class="fa fa-cloud-arrow-down"></i>
			Forcer la mise à jour des vérifications
		</button>
	</div>
{/if}
