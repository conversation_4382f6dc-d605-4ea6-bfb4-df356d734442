<script lang="ts">
	import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader } from '@sveltestrap/sveltestrap';
	import { getTags, redeployService } from '../api';

	let loading = false;
	let error = $state(false);
	let tags = $state([]);
	let currentTag = $state(null);

	async function fetchTags() {
		loading = true;
		error = false;
		try {
			const result = await getTags(service.repository.name);
			tags = result;
		} catch (e) {
			error = true;
		}
		loading = false;
	}

	interface Props {
		service: Service;
		isOpen?: boolean;
		toggle?: any;
		redeploy?: any;
	}

	let {
		service,
		isOpen = $bindable(false),
		toggle = () => (isOpen = !isOpen),
		redeploy = async function (service: Service, tag: string) {
			await redeployService(service.server, service.id, tag);
			isOpen = false;
		}
	}: Props = $props();

	async function onOpening() {
		tags = [];
		currentTag = service.repository.tag;
	}

	async function onOpen() {
		await fetchTags();
	}
</script>

<Modal {isOpen} {toggle} size="xl" on:open={onOpen} on:opening={onOpening}>
	<ModalHeader {toggle}>Modifier le tag</ModalHeader>
	<ModalBody>
		{#if service.repository}
			{#if error}
				<div class="alert alert-danger d-flex align-items-center" role="alert">
					<i class="fa-solid fa-circle-exclamation me-2"></i> Impossible de récupérer les tags de
					l'image {service.repository.image}
				</div>
			{:else}
				<div>
					<p>Image : <span class="fw-bold">{service.repository.name}</span></p>
					<label class="form-label fw-bold" for="redeploy-tag">Tag :</label>
					<select id="redeploy-tag" class="form-select" bind:value={currentTag}>
						{#each tags as tag}
							<option value={tag}>{tag}</option>
						{/each}
					</select>
				</div>
			{/if}
		{/if}
	</ModalBody>
	<ModalFooter>
		<Button color="secondary" on:click={toggle}>Fermer</Button>
		<Button color="danger" on:click={() => redeploy(service, currentTag)}
			>Redéployer avec le tag <span class="fw-bold">{currentTag}</span></Button
		>
	</ModalFooter>
</Modal>
