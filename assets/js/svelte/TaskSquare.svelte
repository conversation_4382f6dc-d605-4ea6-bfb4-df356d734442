<script lang="ts">
	import { formatBytes } from '../utils';

	interface Props {
		task: Task;
		service: Service;
	}

	let { task, service }: Props = $props();

	let cpu = $derived(task.stats.cpu.toFixed(2));
	let cpuPercentage = $derived(task.stats.cpuPercentage.toFixed(0));
	let memory = $derived(formatBytes(task.stats.memory));
	let memoryPercentage = $derived(task.stats.memoryPercentage.toFixed(0));
	let tooltip = $state('');
	let iconClass = $state('');
	let textClass = $state('');

	$effect(() => {
		if (task.stats !== null) {
			iconClass = 'fa-square';
			textClass =
				task.stats?.cpuPercentage > 50 || task.stats?.memoryPercentage > 80
					? 'text-warning'
					: 'text-success';
			tooltip = `${task.state} : ${task.nodeName} - ${cpu}vcpu (${cpuPercentage}%) - ${memory} (${memoryPercentage}%)`;
		} else if (service.status.update !== 'updating') {
			iconClass = 'fa-square';
			textClass = 'text-success';
			tooltip = `${task.state} : ${task.nodeName}`;
		} else {
			iconClass = 'fa-square-bolt fa-beat-fade';
			tooltip = `${task.nodeName} : Démarrage en cours...`;
			textClass = 'text-info';
		}
	});
</script>

<i class="fa-solid {iconClass} me-1 {textClass}" data-bs-toggle="tooltip" title={tooltip}></i>
