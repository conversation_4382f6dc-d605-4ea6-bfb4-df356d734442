<script lang="ts">
	import { onMount } from 'svelte';
	import { fade } from 'svelte/transition';
	import WaitingProgress from './WaitingProgress.svelte';
	import { redeployServices, refreshServices, getService } from '../api';
	import { waitingForUpdates, selectedServices, mercureEventSource } from './stores';
	import RedeployModal from './RedeployModal.svelte';

	let eventSourceInitialized = false;
	let tagModalOpen = $state(false);

	onMount(() => {
		if (!eventSourceInitialized) {
			$mercureEventSource.addEventListener('message', (event) => {
				let message = JSON.parse(event.data);
				const waitingKey = $waitingForUpdates.findIndex(
					(service) => service.serviceId === message.data.id
				);
				if (waitingKey !== -1) {
					if (message.topic === 'service' && $waitingForUpdates[waitingKey].type === 'refresh') {
						$waitingForUpdates[waitingKey].waiting = false;
					} else if (
						message.topic === 'service.reloading' &&
						$waitingForUpdates[waitingKey].type === 'redeploy'
					) {
						$waitingForUpdates[waitingKey].waiting = false;
						getService(
							$waitingForUpdates[waitingKey].serverName,
							$waitingForUpdates[waitingKey].serviceId
						);
					} else if (message.topic === 'service.error') {
						$waitingForUpdates[waitingKey].waiting = false;
						$waitingForUpdates[waitingKey].error = message.data.exception;
					}
				}

				if ($waitingForUpdates.filter((w) => w.waiting === true).length === 0) {
					endWaiting();
				}
			});
			eventSourceInitialized = true;
		}
	});

	async function redeployBatches(e) {
		e.preventDefault();
		let count = $selectedServices.length;
		let multiple = $selectedServices.length > 1;
		if (
			confirm(
				`Êtes-vous sur de vouloir rédéployer le${multiple ? `s ${count}` : ''} service${multiple ? 's' : ''} sélectionné${multiple ? 's' : ''} ?`
			)
		) {
			initWaitingList('redeploy');
			await redeployServices(
				$selectedServices.map((s) => ({
					serverName: s.server,
					serviceId: s.id
				}))
			);
		}
	}

	async function redeployTagBatches(service: Service, tag: string) {
		tagModalOpen = false;
		let count = $selectedServices.length;
		let multiple = $selectedServices.length > 1;
		if (
			confirm(
				`Êtes-vous sur de vouloir rédéployer le${multiple ? `s ${count}` : ''} service${multiple ? 's' : ''} sélectionné${multiple ? 's' : ''} ?`
			)
		) {
			initWaitingList('redeploy');
			await redeployServices(
				$selectedServices.map((s) => ({
					serverName: s.server,
					serviceId: s.id
				})),
				tag
			);
		}
	}

	async function refreshBatches(e) {
		e.preventDefault();
		initWaitingList('refresh');
		await refreshServices(
			$selectedServices.reverse().map((s) => ({
				serverName: s.server,
				serviceId: s.id
			}))
		);
	}

	async function initWaitingList(type: 'refresh' | 'redeploy') {
		$waitingForUpdates = $selectedServices.reverse().map((s) => ({
			type,
			serverName: s.server,
			serviceId: s.id,
			waiting: true
		}));
	}

	async function endWaiting() {
		await new Promise((resolve) => setTimeout(resolve, 1000));
		$waitingForUpdates = [];
	}

	let sameImage = $derived.by(() => {
		if ($selectedServices.length) {
			return $selectedServices.every(
				(s) => s.repository.image === $selectedServices[0].repository.image
			);
		} else {
			return false;
		}
	});
</script>

{#if $selectedServices.length}
	<div transition:fade class="d-flex align-items-center">
		<WaitingProgress />
		<div class="me-3">
			{$selectedServices.length} service{$selectedServices.length > 1 ? 's' : ''} sélectionné{$selectedServices.length >
			1
				? 's'
				: ''}
		</div>
		<button
			class="btn btn-sm btn-info text-white me-2"
			disabled={$waitingForUpdates.length > 0}
			onclick={refreshBatches}
		>
			<i class="fa fa-cloud-arrow-down"></i>
			Forcer la mise à jour des informations
		</button>
		<button
			class="btn btn-sm btn-danger text-white"
			disabled={$waitingForUpdates.length > 0}
			onclick={redeployBatches}
		>
			<i class="fa fa-refresh"></i>
			Redéployer les services
		</button>
		<button
			class="btn btn-sm btn-danger text-white ms-2"
			disabled={!sameImage || $waitingForUpdates.length > 0}
			onclick={(e) => {
				e.preventDefault();
				tagModalOpen = true;
			}}
		>
			<i class="fa fa-tags"></i>
			Modifier le tag
		</button>
	</div>
	<RedeployModal
		service={$selectedServices[0]}
		bind:isOpen={tagModalOpen}
		redeploy={redeployTagBatches}
	/>
{/if}
