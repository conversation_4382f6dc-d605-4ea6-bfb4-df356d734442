<script lang="ts">
	import { downloadCsv } from '../api';

	interface Props {
		services: Service[];
		download?: any;
	}

	let {
		services,
		download = async function (e) {
			e.preventDefault();
			let data = await downloadCsv(services.map((s) => s.id));
			const a = document.createElement('a');
			a.href = 'data:attachment/csv,' + encodeURIComponent(data);
			a.target = '_blank';
			a.download = `tsb-swarm-${new Date().toISOString().split('T')[0]}.csv`;
			document.body.appendChild(a);
			a.click();
			a.remove();
		}
	}: Props = $props();
</script>

<div class="mt-2 text-end">
	<a
		onclick={download}
		target="_blank"
		href="/api/download-csv"
		class="btn btn-sm btn-info text-white"
	>
		<i class="fa fa-download"></i> Télécharger au format CSV
	</a>
</div>
