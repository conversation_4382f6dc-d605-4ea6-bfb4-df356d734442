<script lang="ts">
	interface Props {
		name: any;
		title: any;
		opened?: boolean;
		children?: import('svelte').Snippet;
	}

	let { name, title, opened = true, children }: Props = $props();
</script>

<div class="accordion-item">
	<h2 class="accordion-header" id="panel-{name}">
		<button
			class="accordion-button"
			type="button"
			data-bs-toggle="collapse"
			data-bs-target="#collapse-{name}"
			aria-expanded={opened ? 'true' : 'false'}
			aria-controls="collapse-{name}"
			class:collapsed={!opened}
		>
			{title}
		</button>
	</h2>
	<div
		id="collapse-{name}"
		class="accordion-collapse collapse"
		class:show={opened}
		aria-labelledby="panel-{name}"
	>
		<div class="accordion-body">
			{@render children?.()}
		</div>
	</div>
</div>

<style>
	.accordion-button {
		font-weight: bold;
	}
</style>
