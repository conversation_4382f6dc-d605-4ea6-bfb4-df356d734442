<script lang="ts">
	interface Props {
		details: Details;
	}

	let { details }: Props = $props();
</script>

<ul class="list-group">
	<li class="list-group-item">
		<span class="fw-bold">URL</span> :
		<a target="_blank" href={details.sonarQube.url}>{details.sonarQube.url}</a>
	</li>
</ul>
<div class="mt-2">
	<img height="25px" alt="sonarqube" src={details.sonarQube.badges.alert_status} />
</div>
<div class="mt-2">
	<img height="25px" alt="sonarqube" src={details.sonarQube.badges.reliability_rating} />
	<img height="25px" alt="sonarqube" src={details.sonarQube.badges.security_rating} />
	<img height="25px" alt="sonarqube" src={details.sonarQube.badges.sqale_rating} />
</div>
<div class="mt-2">
	<img height="25px" alt="sonarqube" src={details.sonarQube.badges.ncloc} />
	<img height="25px" alt="sonarqube" src={details.sonarQube.badges.coverage} />
	<img height="25px" alt="sonarqube" src={details.sonarQube.badges.duplicated_lines_density} />
	<img height="25px" alt="sonarqube" src={details.sonarQube.badges.sqale_index} />
</div>
<div class="mt-2">
	<img height="25px" alt="sonarqube" src={details.sonarQube.badges.bugs} />
	<img height="25px" alt="sonarqube" src={details.sonarQube.badges.code_smells} />
	<img height="25px" alt="sonarqube" src={details.sonarQube.badges.security_hotspots} />
	<img height="25px" alt="sonarqube" src={details.sonarQube.badges.vulnerabilities} />
</div>
