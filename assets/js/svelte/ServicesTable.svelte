<script lang="ts">
	import { preventDefault } from 'svelte/legacy';

	import { onMount, tick } from 'svelte';
	import { Tooltip } from 'bootstrap';
	import { debounce, sumBy, uniqBy, get } from 'lodash';

	// TanStack Table imports
	import { writable } from 'svelte/store';
	import {
		createSvelteTable,
		flexRender,
		getCoreRowModel,
		getSortedRowModel,
		getFilteredRowModel,
		renderComponent
	} from '@tanstack/svelte-table';
	import type {
		ColumnDef,
		TableOptions,
		SortingState,
		FilterFn,
		OnChangeFn
	} from '@tanstack/svelte-table';

	import { getServices } from '../api';
	import { mercureEventSource, waitingForUpdates, selectedServices } from './stores';

	// Components
	import Image from './Cells/Image.svelte';
	import Urls from './Cells/Urls.svelte';
	import Replicas from './Cells/Replicas.svelte';
	import DetailsModal from './DetailsModal.svelte';
	import RedeployModal from './RedeployModal.svelte';
	import Details from './Cells/Details.svelte';
	import UpdateBar from './UpdateBar.svelte';
	import Stack from './Cells/Stack.svelte';
	import { default as ServiceComponent } from './Cells/Service.svelte';
	import StatsTable from './StatsTable.svelte';
	import CsvDownloadButton from './CsvDownloadButton.svelte';
	import Php from './Cells/Php.svelte';
	import Symfony from './Cells/Symfony.svelte';
	import Security from './Cells/Security.svelte';
	import FormattedDate from './Cells/FormattedDate.svelte';
	import FormattedDateAndCommit from './Cells/FormattedDateAndCommit.svelte';

	let loading = $state(false);
	let services: Service[] = $state([]);
	let loadingErrors: object[] = $state([]);
	let modalOpen = $state(false);
	let modalService: Service | null = $state(null);
	let tagModalOpen = $state(false);
	let tagModalService: Service | null = $state(null);
	let eventSourceInitialized = false;
	let searchValue: string = $state('');
	let frontWebOnly = $state(true);
	let openedOnly = $state(false);
	// TanStack Table state
	let sorting: SortingState = $state([]);

	onMount(async () => {
		if (services.length === 0) {
			await loadServices();
			let openedService = window.location.hash.replace('#', '');
			if (openedService && getService(openedService)) {
				showDetails(getService(openedService));
			}
		}
		// récupération de la valeur à rechercher dans les paramètres d'URL
		const searchParams = new URLSearchParams(window.location.search);
		if (searchParams.get('search')) {
			searchValue = searchParams.get('search') || '';
			$table.setGlobalFilter(searchValue);
		}
		await tick();
		await new Promise((resolve) => setTimeout(resolve, 100));
		initTooltips();

		if (!eventSourceInitialized) {
			$mercureEventSource.addEventListener('message', (event) => {
				let message = JSON.parse(event.data);
				if (message.topic === 'service') {
					const serviceKey = services.findIndex((service) => service.id === message.data.id);
					if (serviceKey !== -1) {
						services[serviceKey] = message.data;
					}
				}
			});
			eventSourceInitialized = true;
		}
	});

	async function loadServices(force = false) {
		loading = true;
		const response = await getServices(force);
		services = response.services;
		loadingErrors = response.errors;
		loading = false;
		return services;
	}

	function initTooltips() {
		const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
		[...tooltipTriggerList].map((tooltipTriggerEl) => new Tooltip(tooltipTriggerEl));
	}

	function getService(id: string): Service {
		return services.find((s) => s.id === id)!;
	}

	function showDetails(service: Service) {
		modalOpen = true;
		modalService = service;
	}

	function showTags(service: Service) {
		tagModalOpen = true;
		tagModalService = service;
	}

	function getSemVerAccessor(service: Service, value: string) {
		if (value) {
			return value
				.split('.')
				.map((n) => +n + 100000)
				.join('.');
		}
		return service.redeployedAllowed ? '000000.000000.000001' : '000000.000000.000000';
	}

	const columns: ColumnDef<Service>[] = [
		{
			id: 'env',
			accessorKey: 'env',
			header: 'Env',
			cell: (info) => info.getValue()
		},
		{
			id: 'server',
			accessorKey: 'server',
			header: 'Cluster',
			cell: (info) => info.getValue()
		},
		{
			id: 'stack',
			accessorKey: 'stack',
			header: 'Stack',
			cell: (props) =>
				renderComponent(Stack, {
					service: props.row.original,
					services: () => services,
					onclick: (stack: string) => {
						searchValue = stack;
						frontWebOnly = false;
						$table.setGlobalFilter(stack);
					}
				})
		},
		{
			id: 'name',
			accessorKey: 'name',
			header: 'Service',
			cell: (props) =>
				renderComponent(ServiceComponent, {
					service: props.row.original
				})
		},
		{
			id: 'urls',
			accessorFn: (service: Service) =>
				service.repository.image + ' ' + service.repository.imageDigest,
			header: 'Urls',
			enableSorting: false,
			cell: (props) =>
				renderComponent(Urls, {
					service: props.row.original
				})
		},
		{
			id: 'images',
			accessorFn: (service: Service) => service.urls.join(' ') + ' ' + service.middleware,
			header: 'Images',
			enableSorting: false,
			cell: (props) =>
				renderComponent(Image, {
					service: props.row.original
				})
		},
		{
			id: 'replicas',
			accessorFn: (service: Service) => service.status.tasks.running,
			header: 'Replicas',
			cell: (props) =>
				renderComponent(Replicas, {
					service: props.row.original
				})
		},
		{
			id: 'php',
			accessorFn: (service: Service) => {
				return service?.details?.php?.version ?? 0;
			},
			sortingFn: (rowA, rowB) => {
				return getSemVerAccessor(rowA.original, rowA.original?.details?.php?.version).localeCompare(
					getSemVerAccessor(rowB.original, rowB.original?.details?.php?.version)
				);
			},
			header: `PHP`,
			cell: (props) =>
				renderComponent(Php, {
					service: props.row.original
				}),
			enableGlobalFilter: true
		},
		{
			id: 'symfony',
			accessorFn: (service: Service) => service?.details?.symfony?.version,
			sortingFn: (rowA, rowB) => {
				return getSemVerAccessor(
					rowA.original,
					rowA.original?.details?.symfony?.version
				).localeCompare(getSemVerAccessor(rowB.original, rowB.original?.details?.symfony?.version));
			},
			header: `<div class="text-center"><i class="fa-brands fa-symfony fs-5"></i></div>`,
			cell: (props) =>
				renderComponent(Symfony, {
					service: props.row.original
				})
		},
		{
			id: 'security',
			accessorFn: (service: Service) => {
				if (
					service.details === null ||
					!service.details.hasOwnProperty('local-php-security-checker') ||
					service.details['local-php-security-checker'] === null
				) {
					return -1;
				}
				return Object.entries(service.details['local-php-security-checker']?.advisories).length;
			},
			header: 'Sécu',
			cell: (props) =>
				renderComponent(Security, {
					service: props.row.original
				}),
			enableGlobalFilter: false
		},
		{
			id: 'built',
			accessorFn: (service: Service) =>
				service?.details?.php?.buildDate ? new Date(service.details.php.buildDate) : null,
			header: 'Built',
			cell: (props) =>
				renderComponent(FormattedDate, {
					date: props.row.original?.details?.php?.buildDate
				}),
			enableGlobalFilter: false
		},
		{
			id: 'updatedAt',
			accessorFn: (service: Service) => new Date(service.updatedAt),
			header: 'Updated',
			cell: (props) =>
				renderComponent(FormattedDateAndCommit, {
					date: props.row.original.updatedAt,
					service: props.row.original
				}),
			enableGlobalFilter: false
		},
		{
			id: 'details',
			accessorKey: 'id', // Dummy accessor since we don't sort on this
			header: 'Détails',
			enableSorting: false,
			cell: (props) =>
				renderComponent(Details, {
					service: props.row.original,
					showDetails,
					showTags
				}),
			enableGlobalFilter: false
		}
	];

	const searchKeys = [
		'name',
		'id',
		'stack',
		'server',
		'urls',
		'placement',
		'env',
		'repository.image',
		'repository.imageDigest',
		'details.portail.webserviceUrl',
		'details.php.version',
		'details.symfony.version',
		'details.git.lastCommit'
	];

	// Fonction de filtrage personnalisée
	const customGlobalFilter: FilterFn<Service> = (row, columnId, value) => {
		if (!value || value === '' || columnId !== 'env') return false;

		const original = row.original;
		const searches = value.split(' ');
		return searches.every((search: string) => {
			for (const key of searchKeys) {
				if (get(original, key)?.toString()?.includes(search)) {
					return true;
				}
			}
			return false;
		});
	};

	// Fonction de gestion du sorting
	const setSorting: OnChangeFn<SortingState> = (updater) => {
		if (updater instanceof Function) {
			sorting = updater(sorting);
		} else {
			sorting = updater;
		}
		tableOptions.update((old) => ({
			...old,
			state: {
				...old.state,
				sorting
			}
		}));
	};

	// Configuration TanStack Table
	const tableOptions = writable<TableOptions<Service>>({
		data: [],
		columns,
		state: {
			sorting
		},
		getCoreRowModel: getCoreRowModel(),
		getSortedRowModel: getSortedRowModel(),
		getFilteredRowModel: getFilteredRowModel(),
		globalFilterFn: customGlobalFilter,
		onSortingChange: setSorting
	});

	const table = createSvelteTable(tableOptions);

	const handleSearch = debounce((e: any) => {
		$table.setGlobalFilter(String(e?.target?.value));
	}, 100);

	// Filtrage des données de base (avant TanStack Table)
	let baseFilteredServices = $derived.by(() => {
		let tmpServices = services;

		if (frontWebOnly) {
			tmpServices = tmpServices.filter((s) => s.redeployedAllowed);
		}

		if (openedOnly) {
			tmpServices = tmpServices.filter((s) => {
				return (
					s.env !== 'dev' &&
					s.middleware === null &&
					s?.details?.htaccess?.requireUser === undefined
				);
			});
		}

		return tmpServices;
	});

	// Mise à jour des données de la table
	$effect(() => {
		tableOptions.update((options) => ({
			...options,
			data: baseFilteredServices,
			state: {
				...options.state,
				sorting
			}
		}));
	});

	let isAllSelected = $derived(
		$selectedServices.length === $table.getRowModel().rows.length &&
			$table.getRowModel().rows.length > 0
	);
	const selectAll = (e: Event) => {
		e.preventDefault();
		$selectedServices = isAllSelected ? [] : $table.getRowModel().rows.map((row) => row.original);
	};

	// Gestion du comptage des éléments
	let tableData = $derived($table.getRowModel().rows.map((row) => row.original));
	let countsServices = $derived(tableData.length);
	let countsServer = $derived(uniqBy(tableData, (s) => s.server).length);
	let countsStacks = $derived(uniqBy(tableData, (s) => s.stack).length);
	let countsTasks = $derived(sumBy(tableData, (s) => s.tasks.length));

	// Gestion du statut de la mise à jour
	let waitingIds = $derived($waitingForUpdates.filter((w) => w.waiting).map((w) => w.serviceId));
	let updatedIds = $derived($waitingForUpdates.filter((w) => !w.waiting).map((w) => w.serviceId));
	let hasErrorIds = $derived($waitingForUpdates.filter((w) => w.error).map((w) => w.serviceId));

	let sortedColumn = $derived(sorting[0]?.id);
	let sortedDirection = $derived(sorting[0]?.desc ? 'desc' : 'asc');
</script>

{#if loadingErrors.length}
	<div class="alert alert-danger d-flex align-items-center" role="alert">
		<i class="fa fa-info-circle me-2" data-bs-toggle="tooltip" title={loadingErrors[0].exception}
		></i>
		<div>
			Une erreur s'est produite lors de la récupération des services {loadingErrors.length > 1
				? 'des clusters suivants'
				: 'du cluster suivant'} : {loadingErrors.map((e) => e.server).join(', ')}
		</div>
	</div>
{/if}

<div class="table-responsive">
	<div class="gridjs gridjs-container" style="width: 100%">
		<div class="d-flex align-items-center justify-content-between mb-3">
			<div class="d-flex align-items-center">
				{#if !loading}
					<div class="me-2">
						{countsServices} service{countsServices > 1 ? 's' : ''} -
						{countsServer} cluster{countsServer > 1 ? 's' : ''} -
						{countsStacks} stack{countsStacks > 1 ? 's' : ''} -
						{countsTasks} container{countsTasks > 1 ? 's' : ''}
					</div>
					{#if tableData.length > 0}
						<button class="btn btn-sm btn-info text-white me-2" onclick={selectAll}
							>Tout {isAllSelected ? 'désélectionner' : 'sélectionner'}</button
						>
					{/if}
				{/if}
			</div>
			<div>
				<button
					class="btn btn-sm btn-info text-white"
					onclick={preventDefault(() => loadServices(true))}
					disabled={$waitingForUpdates.length > 0}>Mettre à jour la liste des services</button
				>
			</div>
		</div>
		<div class="d-flex align-items-center mb-3">
			<div class="gridjs-search me-2">
				<div class="input-group">
					<input
						bind:value={searchValue}
						onkeyup={handleSearch}
						type="text"
						placeholder="Rechercher..."
						class="gridjs-input gridjs-search-input"
					/>
					{#if searchValue !== ''}
						<span
							class="input-group-text"
							role="button"
							onclick={() => {
								searchValue = '';
								$table.setGlobalFilter('');
							}}
						>
							<i class="fa fa-times"></i>
						</span>
					{:else}
						<span class="input-group-text">
							<i class="fa fa-magnifying-glass"></i>
						</span>
					{/if}
				</div>
			</div>
			<div class="form-check form-switch me-2">
				<input
					class="form-check-input"
					type="checkbox"
					id="frontWebOnly"
					bind:checked={frontWebOnly}
				/>
				<label class="form-check-label" for="frontWebOnly">Frontaux web uniquement</label>
			</div>
			<div class="form-check form-switch">
				<input class="form-check-input" type="checkbox" id="openedOnly" bind:checked={openedOnly} />
				<label class="form-check-label" for="openedOnly">Ouvert sur l'extérieur</label>
			</div>
			<div class="flex-grow-1"></div>
			<UpdateBar />
		</div>
		<div class="gridjs-wrapper" style="height: auto">
			<table role="grid" class="gridjs-table" style="min-width: 100%; height: auto">
				<thead class="gridjs-head">
					{#each $table.getHeaderGroups() as headerGroup}
						<tr class="gridjs-tr">
							<th class="gridjs-th">
								<div class="gridjs-th-content"></div>
							</th>
							{#each headerGroup.headers as header}
								<th
									class="gridjs-th"
									onclick={header.column.getToggleSortingHandler()}
									class:gridjs-th-sort={header.column.getCanSort()}
									class:cursor-pointer={header.column.getCanSort()}
								>
									<div class="gridjs-th-content">
										{@html header.column.columnDef.header}
									</div>
									{#if header.column.getCanSort()}
										<button
											title="Sort column"
											class="gridjs-sort gridjs-sort-{sortedColumn === header.column.id
												? sortedDirection
												: 'neutral'}"
										></button>
									{/if}
								</th>
							{/each}
						</tr>
					{/each}
				</thead>
				<tbody class="gridjs-tbody">
					{#if loading}
						<tr class="gridjs-tr">
							<td colspan="100%" class="gridjs-td">
								<div class="d-flex justify-content-center">
									<div class="spinner-border text-info m-5" role="status">
										<span class="visually-hidden">Loading...</span>
									</div>
								</div>
							</td>
						</tr>
					{:else}
						{#each $table.getRowModel().rows as row (row.original.id)}
							<tr
								class="gridjs-tr"
								class:service-reloading={row.original.status.update === 'updating' ||
									waitingIds.includes(row.original.id)}
							>
								<td class="gridjs-td">
									{#if row.original.redeployedAllowed}
										<div class="text-center">
											{#if waitingIds.includes(row.original.id)}
												<i class="text-info fa-solid fa-circle-notch fa-spin"></i>
											{:else if hasErrorIds.includes(row.original.id)}
												<i class="text-danger fa-solid fa-square-exclamation"></i>
											{:else if updatedIds.includes(row.original.id)}
												<i class="text-success fa-solid fa-square-check"></i>
											{:else if $waitingForUpdates.length === 0}
												<input
													class="form-check-input"
													type="checkbox"
													bind:group={$selectedServices}
													value={row.original}
												/>
											{/if}
										</div>
									{/if}
								</td>
								{#each row.getVisibleCells() as cell}
									<td class="gridjs-td">
										{#if cell.column.columnDef.meta?.cellHTML}
											{@html cell.column.columnDef.cell(cell.getContext())}
										{:else}
											<svelte:component
												this={flexRender(cell.column.columnDef.cell, cell.getContext())}
											/>
										{/if}
									</td>
								{/each}
							</tr>
						{/each}
					{/if}
				</tbody>
			</table>
		</div>
	</div>
</div>

<CsvDownloadButton services={tableData} />

{#if tableData.length}
	<StatsTable services={tableData} />
{/if}

<DetailsModal service={modalService} bind:isOpen={modalOpen} />
<RedeployModal service={tagModalService} bind:isOpen={tagModalOpen} />
