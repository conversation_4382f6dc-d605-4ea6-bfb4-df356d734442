<script lang="ts">
	import { fade } from 'svelte/transition';
	import { waitingForUpdates, selectedServices } from './stores';

	let current = $derived($waitingForUpdates.filter((w) => !w.waiting).length);
	let max = $derived($waitingForUpdates.length);
	let percent = $derived(max > 0 ? (current / max) * 100 : 0);

	async function stopWaiting() {
		await new Promise((resolve) => setTimeout(resolve, 1000));
		$waitingForUpdates = [];
		$selectedServices = [];
	}

	$effect(() => {
		if (current > 0 && max > 0 && current === max) {
			stopWaiting();
		}
	});
</script>

{#if max > 0}
	<div transition:fade class="progress me-2">
		<div
			class="progress-bar progress-bar-striped progress-bar-animated bg-info"
			role="progressbar"
			aria-valuenow={percent}
			aria-valuemin="0"
			aria-valuemax="100"
			style="width: {percent}%"
		>
			{current}/{max}
		</div>
	</div>
{/if}

<style>
	.progress {
		width: 200px;
		border: 1px solid #bababa;
	}
</style>
