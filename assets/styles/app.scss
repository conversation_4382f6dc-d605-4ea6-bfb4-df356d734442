$tooltip-max-width: 1000px;

@import '~bootstrap/scss/bootstrap';
@import 'gridjs.min.css';

body {
	background-color: #e5e5e5;
}

.header {
	background-color: #2e3036;
	height: 65px;
	width: 100%;
	z-index: 99;

	.nav-item {
		color: #fff;
		padding: 10px;
	}
}

tr.service-reloading td.gridjs-td {
	background-color: #fff;
	animation-name: color;
	animation-duration: 2s;
	animation-iteration-count: infinite;
}

tr.webhost-reloading td.gridjs-td {
	background-color: #fff;
	animation-name: color;
	animation-duration: 2s;
	animation-iteration-count: infinite;
}

@keyframes color {
	0% {
		background-color: #fff;
	}
	50% {
		background-color: #17a2b84a;
	}
	100% {
		background-color: #fff;
	}
}

.tooltip-inner {
	white-space: pre-wrap !important;
}

.gridjs-table {
	font-size: 14px;
}
td.gridjs-td {
	padding: 8px 8px;
}
th.gridjs-th {
	padding: 14px 8px;
}

/** WebHosts **/
[data-bs-toggle='collapse'] {
	&[aria-expanded='false'] {
		[data-toggle]:last-child {
			display: none;
		}
	}
	&[aria-expanded='true'] {
		[data-toggle]:first-child {
			display: none;
		}
	}
}

.associated-url {
	line-height: 16px;
}
