parameters:
  kernel.config_dir: "%kernel.project_dir%/config/"

  ### CONFIGURATION WEBSERVICES ###
  webservice.prod:                          '%env(bool:webservice_is_prod_enable)%'
  webservice.path_prod:                     "https://provalliance-wsclient.zefid.fr/"
  webservice.path_preprod:                  "https://preprod-provalliance-wsclient.zefid.fr/"


  # WEBSERVICE
  webservice.programmeGenerique: "%env(NOMENSEIGNE)%"
  webservice.x-api-key: "%env(webservice_x_api_key)%"
  cle_mot_de_passe: "%env(cle_mot_de_passe)%"
  mailing_id_utilisateur: "%env(mailing_id_utilisateur)%"
  webservice.url:
    AuthService: ProvallianceClientClient
    ClientsService: ProvallianceClientClient
    GlobalDefinitionsService: ProvallianceClientTableStatic
    ClientActivateService: false
    AnonymisationService: ProvallianceClientAnonymisationClient