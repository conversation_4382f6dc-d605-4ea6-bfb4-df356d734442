framework:
    messenger:
        # Uncomment this (and the failed transport below) to send failed messages to this transport for later handling.
        # failure_transport: failed

        transports:
            # https://symfony.com/doc/current/messenger.html#transport-configuration
            async: '%env(MESSENGER_TRANSPORT_DSN)%'
            # failed: 'doctrine://default?queue_name=failed'
            # sync: 'sync://'

        routing:
            # Route your messages to the transports
             'App\Message\FetchSwarmpitMessage': async
             'App\Message\RedeployServicesMessage': async
             'App\Message\CheckSSLCertificatesMessage': async
             'App\Message\HealthCheckMessage': async
             'App\Message\RefreshSwarmWebHostsMessage': async

        buses:
          messenger.bus.default:
            middleware:
              - doctrine_ping_connection # reconnects the database connection after a connection failure
              - doctrine_close_connection # closes the database connection after a message has been handled
              - doctrine_open_transaction_logger # logs an error when a Doctrine transaction was opened but not closed
# when@test:
#    framework:
#        messenger:
#            transports:
#                # replace with your transport name here (e.g., my_transport: 'in-memory://')
#                # For more Messenger testing tools, see https://github.com/zenstruck/messenger-test
#                async: 'in-memory://'
