framework:
    cache:
        # Unique name of your app: used to compute stable namespaces for cache keys.
        #prefix_seed: your_vendor_name/app_name

        # The "app" cache stores to the filesystem by default.
        # The data in this cache should persist between deploys.
        # Other options include:

        # Redis
        default_redis_provider: redis://redis?retry_interval=5&read_timeout=5

        # APCu (not recommended with heavy random-write workloads as memory fragmentation can cause perf issues)
        #app: cache.adapter.apcu

        # Namespaced pools use the above "app" backend by default
        pools:
            swarmpit_cache:
              default_lifetime: 86400
              adapter: cache.adapter.redis_tag_aware
              tags: true
            gitlab_cache:
              default_lifetime: 86400
              adapter: cache.adapter.redis_tag_aware
              tags: true
            favicon_cache:
              default_lifetime: 86400
              adapter: cache.adapter.redis_tag_aware
              tags: true