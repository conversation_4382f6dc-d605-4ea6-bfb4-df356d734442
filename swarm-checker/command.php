#!/usr/bin/env php
<?php

require __DIR__ . '/vendor/autoload.php';

error_reporting(E_ALL & ~E_DEPRECATED);

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\SingleCommandApplication;
use Symfony\Component\Process\Process;
use Symfony\Component\Yaml\Yaml;

(new SingleCommandApplication())
    ->setName('Swarm Checker')
    ->setVersion('1.0.4')
    ->setHelp('')
    ->addArgument('projectPath', InputArgument::OPTIONAL, '', '/var/www/html')
    ->addOption('install', 'i', InputOption::VALUE_NONE, 'Install dependencies')
    ->addOption('update', 'u', InputOption::VALUE_NONE, 'Self-update swarm-checker to last version')
    ->setCode(function (InputInterface $input, OutputInterface $output) {
        $projectPath = $input->getArgument('projectPath');
        $rootPath = '/var/www/_anetversion';

        if ($input->getOption('install')) {
            return Command::SUCCESS;
        }

        if ($input->getOption('update')) {
            $output->writeln(sprintf('Updating <info>swarm-checker</info>...'));
            selfUpdate($output);

            return Command::SUCCESS;
        }

        $startTime = microtime(true);

        $response = [
            'date' => (new DateTime())->format('Y-m-d H:i:s'),
        ];

        $composerPath = $projectPath . '/composer.json';

        if (file_exists($composerPath)) {
            try {
                $response['composer'] = getComposerInfos($projectPath);
            } catch (Exception $e) {
                $response['composer'] = 'error';
            }

            try {
                $response['local-php-security-checker'] = securityChecker($rootPath, $projectPath);
            } catch (Exception $e) {
                $response['local-php-security-checker'] = 'error';
            }

            try {
                $response['symfony'] = getSymfonyInfos($projectPath);
            } catch (Exception $e) {
                $response['env'] = 'error';
            }
        }

        $wordpressPath = $projectPath . '/public/wp-includes/version.php';

        if (file_exists($wordpressPath)) {
            try {
                $response['wordpress'] = getWordpressInfos($projectPath);
            } catch (Exception $e) {
                $response['env'] = 'error';
            }
        }

        try {
            $response['env'] = parseEnv($projectPath);
        } catch (Exception $e) {
            $response['env'] = 'error';
        }

        try {
            $response['git'] = parseGit($projectPath);
        } catch (Exception $e) {
            $response['git'] = 'error';
        }

        try {
            $response['portail'] = parsePortailInfos($projectPath);
        } catch (Exception $e) {
            $response['portail'] = 'error';
        }

        try {
            $response['php'] = parsePhpInfos();
        } catch (Exception $e) {
            $response['php'] = 'error';
        }

        try {
            $response['htaccess'] = parseHtaccessInfos($projectPath);
        } catch (Exception $e) {
            $response['htaccess'] = 'error';
        }

        $sonarQubePath = $projectPath . '/sonar-project.properties';
        if (file_exists($sonarQubePath)) {
            try {
                $response['sonarQube'] = getSonarQubeInfos($sonarQubePath);
            } catch (Exception $e) {
                $response['sonarQube'] = 'error';
            }
        }

        $response['timing'] = microtime(true) - $startTime;

        $output->writeln(json_encode($response, JSON_PRETTY_PRINT));

        return Command::SUCCESS;
    })
    ->run();

/**
 * Récupération de l'execution de composer audit.
 */
function securityChecker($rootPath, $projectPath)
{
    $executableFinder = new Symfony\Component\Process\ExecutableFinder();
    $composerPath = $executableFinder->find('composer');
    if ($composerPath) {
        $composerCommand = new Process([
            $composerPath,
            sprintf('--working-dir=%s', $projectPath),
            'audit',
            '--format=json',
        ], null, [
            'COMPOSER_HOME' => '/root/.config/composer',
            'COMPOSER_AUDIT_ABANDONED' => 'ignore',
        ]);
        $composerCommand->run();
        $packages = json_decode($composerCommand->getOutput(), true);

        return $packages;
    }

    throw new Exception('Composer not found');
}

/**
 * Récupération des variables d'environnement à partir des fichiers .env et .env.local.
 */
function parseEnv($projectPath)
{
    $envVars = [];
    $envFilePath = $projectPath . '/.env';
    $envLocalFilePath = $envFilePath . '.local';

    if ($envFilePath) {
        $parser = new Dotenv\Parser\Parser();
        if (file_exists($envFilePath)) {
            $resultEnv = $parser->parse(file_get_contents($envFilePath));
            foreach ($resultEnv as $item) {
                $envVars[$item->getName()] = $item->getValue()->get()->getChars();
            }
        }
        if (file_exists($envLocalFilePath)) {
            $resultEnvLocal = $parser->parse(file_get_contents($envLocalFilePath));
            foreach ($resultEnvLocal as $item) {
                $envVars[$item->getName()] = $item->getValue()->get()->getChars();
            }
        }
    }

    return $envVars;
}

/**
 * Récupération des informations des paquets Composer installés
 * Si composer n'est pas installé on parse le fichier composer.lock à la place.
 */
function getComposerInfos($projectPath)
{
    $executableFinder = new Symfony\Component\Process\ExecutableFinder();
    $composerPath = $executableFinder->find('composer');
    if ($composerPath) {
        $composerCommand = new Process([
            $composerPath,
            sprintf('--working-dir=%s', $projectPath),
            'show',
            '--format=json',
        ], null, ['COMPOSER_HOME' => '/root/.config/composer']);
        $composerCommand->run();
        $packages = json_decode($composerCommand->getOutput(), true);
        if ($packages) {
            $packages['installed'] = array_map(function ($package) {
                return [
                    'name' => $package['name'],
                    'version' => $package['version'],
                ];
            }, $packages['installed']);

            return $packages;
        }
    }

    $lockFile = $projectPath . '/composer.lock';
    if (file_exists($lockFile)) {
        $lockContent = json_decode(file_get_contents($lockFile), true);

        if ($lockContent) {
            return [
                'installed' => array_map(function ($package) {
                    return [
                        'name' => $package['name'],
                        'version' => $package['version'],
                    ];
                }, $lockContent['packages']),
            ];
        }
    }

    throw new Exception('Composer lock file not found');
}

/**
 * Récupération des informations du dépot Git.
 */
function parseGit($projectPath)
{
    $executableFinder = new Symfony\Component\Process\ExecutableFinder();
    $gitPath = $executableFinder->find('git');
    if ($gitPath) {
        $git = new CzProject\GitPhp\Git();
        $repo = $git->open($projectPath);

        $remoteUrl = $repo->execute('config', '--get', 'remote.origin.url')[0];
        $gitlabUrl = 'gitlab.alienor.net';
        $url = str_replace(sprintf('git@%s:', $gitlabUrl), sprintf('https://%s/', $gitlabUrl), $remoteUrl);
        $url = str_replace('.git', '', $url);

        // Récupération des 10 derniers commits
        $output = $repo->execute('log', '-10', "--date=format:'%Y-%m-%d %H:%M:%S'");
        $history = [];
        foreach ($output as $line) {
            if (0 === strpos($line, 'commit')) {
                if (!empty($commit)) {
                    array_push($history, $commit);
                    unset($commit);
                }
                $commit['hash'] = trim(substr($line, strlen('commit')));
                $commit['url'] = $url . '/-/commit/' . $commit['hash'];
            } elseif (0 === strpos($line, 'Author')) {
                $commit['author'] = trim(substr($line, strlen('Author:')));
            } elseif (0 === strpos($line, 'Date')) {
                $commit['date'] = trim(substr($line, strlen('Date:')), " '");
            } else {
                if (!isset($commit['message'])) {
                    $commit['message'] = '';
                }
                $commit['message'] .= trim($line);
            }
        }

        // Récupération du dernier tag
        try {
            $tag = $repo->execute('describe', '--tags');
        } catch (Exception $exception) {
            $tag = null;
        }

        return [
            'url' => $url,
            'branch' => $repo->getCurrentBranchName(),
            'tag' => $tag,
            'history' => $history,
            'lastCommit' => $repo->getLastCommitId()->toString(),
        ];
    } else {
        return ['not installed !'];
    }
}

/**
 * Récupération des infos sur la version de Symfony.
 */
function getSymfonyInfos($projectPath)
{
    $symfonyPath = $projectPath . '/bin/console';
    if (file_exists($symfonyPath)) {
        $symfonyCommand = new Process([
            'php',
            $symfonyPath,
            '--version',
            '--no-ansi',
        ]);
        $symfonyCommand->run();
        preg_match("/((?:[0-9]+\.?)+)/i", $symfonyCommand->getOutput(), $matches);
        $version = $matches[1];

        return [
            'version' => $version,
        ];
    }

    return [];
}

function getWordpressInfos(string $rootPath)
{
    $path = $rootPath . '/public/wp-includes/version.php';
    if (file_exists($path)) {
        $wordpressCommand = Process::fromShellCommandline(sprintf("grep wp_version %s | awk -F \"'\" '{print $2}'", $path));
        $wordpressCommand->run();
        preg_match("/((?:[0-9]+\.?)+)/i", $wordpressCommand->getOutput(), $matches);

        return [
            'version' => $matches[1],
        ];
    }

    return null;
}
/**
 * Récupération des infos sur la version de Symfony.
 */
function parseHtaccessInfos($projectPath)
{
    $parsed = [
        'exists' => false,
    ];
    $htacessPath = $projectPath . '/public/.htaccess';
    if (file_exists($htacessPath)) {
        $parsed['exists'] = true;
        $content = file_get_contents($htacessPath);
        $lines = explode("\n", $content);
        foreach ($lines as $line) {
            if (0 === strpos(trim($line), 'Require user')) {
                $parsed['requireUser'] = array_values(array_filter(explode(' ', substr($line, 12))));
            }
        }
    }

    return $parsed;
}

/**
 * Recherche de l'url du webservice dans les fichiers suivants :
 * config/parametersClient.yaml / .yml
 * config/packages/alienor_aquitem_web_service_parser.yaml / .yml
 * config/packages/alienor_aquitem_web_service_bundle.yaml / .yml.
 */
function parsePortailInfos($projectPath)
{
    $infos = [];

    // Recherche de parametersClient
    $parametersClientPath = $projectPath . '/config/parametersClient.yaml';
    if (!file_exists($parametersClientPath)) {
        $parametersClientPath = str_replace('.yaml', '.yml', $parametersClientPath);
    }

    // Recherche du fichier de config du bundle
    $bundleName = 'alienor_aquitem_web_service_parser';
    $bundleConfigPath = sprintf('%s/config/packages/%s.yaml', $projectPath, $bundleName);
    if (!file_exists($bundleConfigPath)) {
        // Recherche si le fichier .yml existe au lieu du .yaml
        $bundleConfigPath = str_replace('.yaml', '.yml', $bundleConfigPath);
        if (!file_exists($bundleConfigPath)) {
            $bundleName = 'alienor_aquitem_web_service_bundle';
            // Recherche avec un autre nom de bundle
            $bundleConfigPath = str_replace('alienor_aquitem_web_service_parser', $bundleName, $bundleConfigPath);
            if (!file_exists($bundleConfigPath)) {
                // Recherche si le fichier .yaml existe au lieu du .yml
                $bundleConfigPath = str_replace('.yml', '.yaml', $bundleConfigPath);
            }
        }
    }

    $env = parseEnv($projectPath);

    $webserviceKey = 'webservice.path_preprod';
    if (isset($env['webservice_is_prod_enable']) && 'true' === $env['webservice_is_prod_enable']) {
        $webserviceKey = 'webservice.path_prod';
    }
    // Spécificité portail demat
    if (isset($env['ENABLE_AQUITEM_WEBSERVICE_PROD']) && 'true' === $env['ENABLE_AQUITEM_WEBSERVICE_PROD']) {
        $webserviceKey = 'webservice.path_prod';
    }

    // Sur les portail enseigne l'url est présente dans le fichier parametersClient
    if (file_exists($parametersClientPath)) {
        $parametersClient = Yaml::parseFile($parametersClientPath);

        if (isset($parametersClient['parameters'][$webserviceKey])) {
            $infos['webserviceUrl'] = $parametersClient['parameters'][$webserviceKey];
        }
    }

    // Sur les portails client l'url est dans le fichier de conf du bundle
    if (!isset($infos['webserviceUrl']) && file_exists($bundleConfigPath)) {
        $webserviceKey = str_replace('.', '_', $webserviceKey);
        $parametersBundle = Yaml::parseFile($bundleConfigPath);
        if (isset($parametersBundle[$bundleName][$webserviceKey])) {
            $infos['webserviceUrl'] = $parametersBundle[$bundleName][$webserviceKey];
        }
    }

    // Sur les portails demat l'url est dans le service.yaml
    if (!isset($infos['webserviceUrl'])) {
        $servicesPath = $projectPath . '/config/services.yaml';
        if (!file_exists($servicesPath)) {
            $servicesPath = str_replace('.yaml', '.yml', $servicesPath);
        }
        if (file_exists($servicesPath)) {
            $services = Yaml::parseFile($servicesPath);
            if (isset($services['parameters']['allParameters'][$webserviceKey])) {
                $infos['webserviceUrl'] = $services['parameters']['allParameters'][$webserviceKey];
            }
        }
    }

    return $infos;
}

function parsePhpInfos()
{
    $buildDate = null;
    try {
        ob_start();
        phpinfo();
        $phpinfoAsString = ob_get_contents();
        ob_get_clean();
        preg_match("/Build Date => (.*)\n/i", $phpinfoAsString, $matches);
        $buildDate = date('Y-m-d H:i:s', strtotime($matches[1]));
    } catch (Exception $e) {
    }

    return [
        'buildDate' => $buildDate,
        'version' => phpversion(),
    ];
}

function getSonarQubeInfos(string $sonarQubePath)
{
    if (file_exists($sonarQubePath)) {
        $properties = file_get_contents($sonarQubePath);
        preg_match("/sonar.projectKey=(.*)\n/i", $properties, $matches);

        return [
            'projectKey' => $matches[1] ?? null,
        ];
    }
}

function selfUpdate(OutputInterface $output)
{
    // Clé d'api au niveau du projet avec le scope read_api et le rôle Reporter
    $url = 'https://oauth2:<EMAIL>/api/v4/projects/dev-interne%2Ftdb-docker-swarm/packages/generic/swarm-checker/0.0.1';
    file_put_contents(Phar::running(false), file_get_contents($url . '/swarm-checker.phar'));
    $output->writeln(sprintf('<info>swarm-checker</info> installed at path <info>%s</info>', Phar::running(false)));
}
