image: gitlab.alienor.net:5050/dev-interne/tdb-docker-swarm:dev

default:
  tags:
    - anetdev

cache:
  paths:
    - vendor/
    - assets/vendor/
  key:
    files:
      - composer.lock

stages:
  - build-extension
  - build-image
  - deploy

build-chrome-extension:
  stage: build-extension
  image: node:22-alpine
  before_script:
    - npm install -g crx3
    - apk add curl
  script:
    - |
      VERSION=$(grep '"version"' chrome-extension/manifest.json | sed 's/.*"version": "\([^"]*\)".*/\1/')
      echo "Building Chrome Extension v${VERSION}..."
      mkdir -p dist
      crx3 --crx "./dist/alienor-webhost-extension-${VERSION}.crx" --key ./scripts/chrome-extension.pem ./chrome-extension
      sh ./scripts/generate-update-xml.sh
      # Upload CRX file
      curl --header "JOB-TOKEN: $CI_JOB_TOKEN" \
           --upload-file "dist/alienor-webhost-extension-${VERSION}.crx" \
           "${CI_API_V4_URL}/projects/${CI_PROJECT_ID}/packages/generic/chrome-extension/${VERSION}/alienor-webhost-extension-${VERSION}.crx"

      # Upload update XML
      curl --header "JOB-TOKEN: $CI_JOB_TOKEN" \
           --upload-file "dist/updates.xml" \
           "${CI_API_V4_URL}/projects/${CI_PROJECT_ID}/packages/generic/chrome-extension/${VERSION}/updates.xml"

      # Upload update XML as latest
      curl --header "JOB-TOKEN: $CI_JOB_TOKEN" \
           --upload-file "dist/updates.xml" \
           "${CI_API_V4_URL}/projects/${CI_PROJECT_ID}/packages/generic/chrome-extension/latest/updates.xml"
  artifacts:
    paths:
      - dist/
    expire_in: 1 week
  only:
    changes:
      - chrome-extension/**/*
    refs:
      - master
      - develop
      - feature/chrome-extension

build-image-prod:
  stage: build-image
  image: docker:latest
  variables:
    DOCKER_TLS_CERTDIR: '/certs'
  before_script:
    - mkdir -p $HOME/.docker
    - echo $DOCKER_AUTH_CONFIG > $HOME/.docker/config.json
  script:
    - docker compose -f compose.yaml -f compose.prod.yaml build php
    - echo "Image build successfully"
    - docker compose -f compose.yaml -f compose.prod.yaml push php
    - echo "Image push successfully"
  only:
    - master

deploy-prod:
  image: gitlab.alienor.net:5050/dev-docker/docker-tools
  stage: deploy
  needs: ['build-image-prod']
  environment:
    name: production
    url: https://tdb-swarm.int.alienor.net
  variables:
    GIT_STRATEGY: none
  script:
    - docker-tools update anet-dev web-tdb-swarm_front_web -i gitlab.alienor.net:5050/dev-interne/tdb-docker-swarm:prod --detach
    - docker-tools update anet-dev web-tdb-swarm_scheduler -i gitlab.alienor.net:5050/dev-interne/tdb-docker-swarm:prod --detach
    - docker-tools update anet-dev web-tdb-swarm_consumer -i gitlab.alienor.net:5050/dev-interne/tdb-docker-swarm:prod --detach
  only:
    - master
