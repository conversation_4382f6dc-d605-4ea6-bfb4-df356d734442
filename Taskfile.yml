# https://taskfile.dev

version: '3'

vars:
  GITLAB_PROJECT: 'dev-interne/tdb-docker-swarm'
  DOCKER_IMAGE: 'gitlab.alienor.net:5050/{{.GITLAB_PROJECT}}'
  PREPROD_SERVER: 'anet-dev'
  PREPROD_SERVICE: 'web-tdb-swarm_front_web'
  PROD_SERVER: 'anet-dev'
  PROD_SERVICE: 'web-tdb-swarm_front_web'
  PROD_SERVICE_CONSUMER: 'web-tdb-swarm_consumer'
  PROD_SERVICE_SCHEDULER: 'web-tdb-swarm_scheduler'
  DOCKER_COMP: 'docker compose'
  DOCKER_COMP_PROD: 'docker compose -f compose.yaml -f compose.prod.yaml'
  DOCKER_CONTAINER_NAME: 'php'
  PROD_TAG: 'prod'
  GITLAB_TOKEN: '**************************'

tasks:
  default:
    cmds:
      - task: docker:exec
    silent: true

  ###
  #
  # Prod
  #
  ###
  build-prod:
    desc: Build the production image
    cmds:
      - '{{.DOCKER_COMP_PROD}} build {{.DOCKER_CONTAINER_NAME}}'
  push-prod:
    desc: Push the production image
    cmds:
      - '{{.DOCKER_COMP_PROD}} push {{.DOCKER_CONTAINER_NAME}}'
  deploy-prod:
    desc: Deploy the app to prod
    cmds:
      - 'docker-tools update {{.PROD_SERVER}} {{.PROD_SERVICE}} -i {{.DOCKER_IMAGE}}:{{.PROD_TAG}}'
      - 'docker-tools update {{.PROD_SERVER}} {{.PROD_SERVICE_CONSUMER}} -i {{.DOCKER_IMAGE}}:{{.PROD_TAG}}'
      - 'docker-tools update {{.PROD_SERVER}} {{.PROD_SERVICE_SCHEDULER}} -i {{.DOCKER_IMAGE}}:{{.PROD_TAG}}'
  build-and-deploy-prod:
    desc: Build and deploy the app to prod
    cmds:
      - task: build-prod
      - task: push-prod
      - task: deploy-prod
  swarm-checker-compile:
    desc: Compile the swarm-checker phar
    cmds:
      - '{{.DOCKER_COMP}} exec -u www-data php rm swarm-checker/dist/swarm-checker.phar'
      - '{{.DOCKER_COMP}} exec -u www-data php php -d phar.readonly=off vendor/bin/phar-composer build swarm-checker swarm-checker/dist/'
  swarm-checker-push:
    desc: Push the swarm-checker phar to gitlab
    vars:
      VERSION: '1.0.4'
      URL: 'https://gitlab.alienor.net/api/v4/projects/dev-interne%2Ftdb-docker-swarm/packages/generic/swarm-checker/{{.VERSION}}'

    cmds:
      - 'curl --header "PRIVATE-TOKEN: {{.GITLAB_TOKEN}}" --upload-file swarm-checker/dist/swarm-checker.phar "{{.URL}}/swarm-checker.phar"'
      - 'curl --header "PRIVATE-TOKEN: {{.GITLAB_TOKEN}}" --upload-file swarm-checker/dist/checker.php "{{.URL}}/checker.php"'
  lint:
    desc: Lint php and twig files
    cmds:
      - 'docker compose exec -e PHP_CS_FIXER_IGNORE_ENV=1 php sudo -Eu www-data php vendor/bin/php-cs-fixer fix'
      - 'npm run lint'
  extension-build:
    desc: Build the chrome extension
    cmds:
      - './scripts/build-chrome-extension.sh'
      - './scripts/generate-update-xml.sh'
    generates:
      - dist/alienor-webhost-extension-*.crx
      - dist/updates.xml
  extension-deploy:
    desc: Deploy the chrome extension
    vars:
      URL: 'https://gitlab.alienor.net/api/v4/projects/dev-interne%2Ftdb-docker-swarm/packages/generic/chrome-extension'
      VERSION:
        sh: |
          grep '"version"' chrome-extension/manifest.json | sed 's/.*"version": "\([^"]*\)".*/\1/'
    cmds:
      - 'echo "Deploying version {{.VERSION}}..."'
      - 'curl --header "PRIVATE-TOKEN: {{.GITLAB_TOKEN}}" --upload-file dist/alienor-webhost-extension-{{.VERSION}}.crx "{{.URL}}/{{.VERSION}}/alienor-webhost-extension-{{.VERSION}}.crx"'
      - 'curl --header "PRIVATE-TOKEN: {{.GITLAB_TOKEN}}" --upload-file dist/updates.xml "{{.URL}}/{{.VERSION}}/updates.xml"'
      - 'curl --header "PRIVATE-TOKEN: {{.GITLAB_TOKEN}}" --upload-file dist/updates.xml "{{.URL}}/latest/updates.xml"'
    sources:
      - dist/alienor-webhost-extension-*.crx
      - dist/updates.xml
