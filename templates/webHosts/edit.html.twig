{% extends "base.html.twig" %}

{% form_theme form 'bootstrap_5_layout.html.twig' %}

{% block body %}
    <div class="container-sm pt-5">
        <div class="card p-3">
            <h4 class="card-title mb-3">Modifier un hébergement</h4>
            {{ form_start(form) }}
            {{ form_row(form.name) }}
            {{ form_row(form.urls) }}
            {{ form_row(form.confluenceUrl) }}
            {{ form_row(form.databaseExplorerUrl) }}
            {{ form_row(form.associatedUrls) }}
            {{ form_row(form.expectedVisibility) }}
            {{ form_row(form.environnement) }}
            {{ form_row(form.gitlabRemoteUrl) }}
            {{ form_row(form.gitlabActiveBranch) }}
            <hr class="mt-4">
            <h5 class="mt-4">Configuration</h5>
            <div class="mb-3">
                Type : <span class="badge bg-primary">{{ webHost.configuration.type }}</span>
            </div>
            {% if webHost.configuration.type == 'vm' %}
                {{ form_row(form.vmIp) }}
                {{ form_row(form.vmLocation) }}
            {% elseif webHost.configuration.type == 'swarm' %}
                {{ form_row(form.swarmClusterName) }}
                {{ form_row(form.swarmSwarmpitUrl) }}
                {{ form_row(form.swarmStack) }}
                {{ form_row(form.swarmServiceName) }}
            {% endif %}
            {{ form_rest(form) }}

            <button type="submit" class="btn btn-primary">Modifier</button>
            <a href="{{ path('app_webHosts') }}" class="btn btn-secondary">Retourner à la liste</a>
            {{ form_end(form) }}
        </div>
    </div>
{% endblock body %}