<!DOCTYPE html>
<html>
    <head>
        <meta charset="UTF-8">
        <title>{% block title %}TDB Docker Swarm{% endblock %}</title>
        <link rel="icon" type="image/png" href="{{ asset('favicon-32x32.png') }}" sizes="32x32" />
        <link href="https://icones.mydataviz.fr/v6/css/all.min.css" rel="stylesheet" type="text/css"/>
        {% block stylesheets %}
            {{ vite_entry_link_tags('theme') }}
        {% endblock %}
        {% block javascripts %}{% endblock %}
    </head>
    <body>
        {% include "header.html.twig" %}
        {% block body %}{% endblock %}
    </body>
    <script type="application/json" id="mercure-url">
        {{ mercure([
            'service',
            'service.error',
            'service.reloading',
            'webhost.updated'
        ])|json_encode(constant('JSON_UNESCAPED_SLASHES') b-or constant('JSON_HEX_TAG'))|raw }}
    </script>
</html>
