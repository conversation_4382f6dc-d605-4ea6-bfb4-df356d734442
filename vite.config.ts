import { defineConfig } from 'vite';
import symfonyPlugin from 'vite-plugin-symfony';
import { svelte } from '@sveltejs/vite-plugin-svelte';
import { dirname, resolve } from 'node:path';
import { fileURLToPath } from 'node:url';
const projectDir = dirname(fileURLToPath(import.meta.url));

export default defineConfig({
	plugins: [
		svelte({
			onwarn(warning, defaultHandler) {
				if (warning.code.startsWith('a11y_')) return; // Suppress all accessibility warnings
				// Handle all other warnings normally
				defaultHandler(warning);
			}
		}),
		symfonyPlugin()
	],
	build: {
		rollupOptions: {
			input: {
				app: './assets/app.js',
				webHost: './assets/webHost.js',
				theme: './assets/styles/app.scss'
			}
		}
	},
	scss: {
		quietDeps: true
	},
	resolve: {
		alias: {
			'~bootstrap': resolve(projectDir, 'node_modules/bootstrap'),
			'~': resolve(projectDir, 'assets'),
			'~project': projectDir
		}
	}
});
