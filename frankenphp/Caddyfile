{
	{$CADDY_GLOBAL_OPTIONS}

    servers {
	    trusted_proxies static private_ranges
    }

	frankenphp {
		{$FRANKENPHP_CONFIG}
	}

	# https://caddyserver.com/docs/caddyfile/directives#sorting-algorithm
	order mercure after encode
	order vulcain after reverse_proxy
	order php_server before file_server
}

{$CADDY_EXTRA_CONFIG}

{$SERVER_NAME:localhost} {

    route /_adminer* {
        @blocked not client_ip private_ranges *************/24 *************/24 *************/24 ********/24 ********/24 *************/24 *************/24
        respond @blocked "" 404

        root * /var/www/_adminer
        php_server
    }

    route /_anetversion* {
        @blocked not client_ip private_ranges *************/24 *************/24 *************/24 ********/24 ********/24 *************/24 *************/24
        respond @blocked "" 404

        root * /var/www/_anetversion
        uri strip_prefix /_anetversion
        php_server
    }

    @useBasicAuth {
        expression {env.BASIC_AUTH_ENABLED} != ""
    }

    handle @useBasicAuth {
        basic_auth {
            {$BASIC_AUTH_LOGIN_1}
            {$BASIC_AUTH_LOGIN_2}
            {$BASIC_AUTH_LOGIN_3}
        }
    }

	log {
		# Redact the authorization query parameter that can be set by Mercure
		format filter {
			wrap console
			fields {
				uri query {
					replace authorization REDACTED
				}
			}
		}
	}

	root * /var/www/html/public
	encode zstd br gzip

	mercure {
		# Transport to use (default to Bolt)
		transport_url {$MERCURE_TRANSPORT_URL:bolt:///data/mercure.db}
		# Publisher JWT key
		publisher_jwt {env.MERCURE_PUBLISHER_JWT_KEY} {env.MERCURE_PUBLISHER_JWT_ALG}
		# Subscriber JWT key
		subscriber_jwt {env.MERCURE_SUBSCRIBER_JWT_KEY} {env.MERCURE_SUBSCRIBER_JWT_ALG}
		# Allow anonymous subscribers (double-check that it's what you want)
		anonymous
		# Enable the subscription API (double-check that it's what you want)
		subscriptions
		# Extra directives
		{$MERCURE_EXTRA_DIRECTIVES}
	}

	vulcain

	{$CADDY_SERVER_EXTRA_DIRECTIVES}

	# Disable Topics tracking if not enabled explicitly: https://github.com/jkarlin/topics
	header ?Permissions-Policy "browsing-topics=()"

	php_server
}