# Tableau de bord Docker Swarm

## Installation

Créer et compléter le fichier de configuration **config/swarmpit.yaml**

```yaml
servers:
  - name: swarmpit-dev
    env: dev
    url: 'https://swarmpit-dev.alienor.net/'
    token: 'xxxx'
  - name: swarmpit-preprod
    env: preprod
    url: 'https://swarmpit-preprod.alienor.net/'
    token: 'xxxx'
```

Renseigner le login/password (format base64) qui permet d'accèder aux sites derrières des htaccess dans **.env.local**

```yaml
DETAILS_HTTP_AUTH_PASSWORD=xxxxxx
```

C<PERSON>er une clé d'API gitlab avec les scopes suivants : api, read_registry

```yaml
GITLAB_API_KEY=glpat-xxxxxx
```
