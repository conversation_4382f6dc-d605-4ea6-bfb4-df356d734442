{"type": "module", "devDependencies": {"@hotwired/stimulus": "^3.0.0", "@sveltejs/vite-plugin-svelte": "^5.1.0", "@symfony/stimulus-bridge": "^3.2.0", "@tanstack/svelte-table": "npm:tanstack-table-8-svelte-5@^0.1", "ts-loader": "^9.4.1", "vite": "^6.3.5", "vite-plugin-symfony": "^8.1.1"}, "license": "UNLICENSED", "private": true, "scripts": {"dev": "vite", "build": "vite build", "lint": "prettier --plugin-search-dir . --write ."}, "dependencies": {"@popperjs/core": "^2.11.6", "@prettier/plugin-php": "^0.19.1", "@sveltestrap/sveltestrap": "^7.1.0", "@tsconfig/svelte": "^3.0.0", "@types/axios": "^0.14.0", "@types/bootstrap": "^5.2.4", "@types/lodash": "^4.14.185", "axios": "^0.27.2", "bootstrap": "^5.3.6", "dotenv": "^16.0.2", "eslint": "^8.23.1", "fast-sort": "^3.2.0", "javascript-time-ago": "^2.5.7", "lodash": "^4.17.21", "prettier": "^3.5.3", "prettier-plugin-svelte": "^3.4.0", "sass": "1.64.2", "sass-loader": "^13.0.2", "svelte": "^5.34.1", "svelte-check": "^4.0.0", "svelte-preprocess": "^6.0.0", "tanstack/svelte-table": "npm:tanstack-table-8-svelte-5@^0.1", "typescript": "^5.5.0"}, "overrides": {"@tanstack/svelte-table": "npm:tanstack-table-8-svelte-5@^0.1"}}