{"compilerOptions": {"composite": true, "target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "isolatedModules": true, "moduleDetection": "force", "noEmit": true, "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "allowJs": true, "paths": {"~/*": ["./assets/*"], "~project/*": ["./*"]}}, "include": ["assets"], "references": [{"path": "./tsconfig.node.json"}]}